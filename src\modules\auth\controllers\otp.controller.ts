import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Get,
  UseGuards,
  Req,
  Res,
  UseInterceptors,
} from '@nestjs/common';
import { OtpService } from '../services/otp.service';
import { Response } from 'express';
import { ImageUrlInterceptor } from '../../../common/interceptors/image-url.interceptor';
import { UsePermanentUrls } from '../../../common/decorators/image.decorator';
import { AuthService } from '../services/auth.service';
import { Public } from '../../../common/decorators/public.decorator';
import { RequestOtpDto } from '../dto/request-otp.dto';
import { VerifyOtpDto } from '../dto/verify-otp.dto';

@Controller('auth/otp')
export class OtpController {
  constructor(
    private readonly otpService: OtpService,
    private readonly authService: AuthService,
  ) {}

  @Post('request')
  @Public()
  @HttpCode(HttpStatus.OK)
  async requestOtp(
    @Body() requestOtpDto: RequestOtpDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const result = await this.otpService.requestOtp(requestOtpDto.phoneNumber);

    response.locals.message = result;

    return '';
  }

  @Post('verify')
  @Public()
  @HttpCode(HttpStatus.OK)
  async verifyOtp(
    @Body() verifyOtpDto: VerifyOtpDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const result = await this.otpService.verifyOtp(verifyOtpDto);

    response.locals.message = 'OTP Verified Successfully';

    return result;
  }

  @Post('resend')
  @Public()
  @HttpCode(HttpStatus.OK)
  async resendOtp(
    @Body() requestOtpDto: RequestOtpDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const result = await this.otpService.resendOtp(requestOtpDto.phoneNumber);
    response.locals.message = 'OTP Resent Successfully';
    return result;
  }

  @Get('profile')
  @UseInterceptors(ImageUrlInterceptor)
  @UsePermanentUrls(['imageUrl'])
  getProfile(@Req() req) {
    (req as any).res.locals.message = 'User details fetched successfully';
    return this.authService.getUserDetails(req.user);
  }
}
