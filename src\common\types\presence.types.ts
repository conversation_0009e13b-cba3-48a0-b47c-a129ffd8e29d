export interface MemberPresence {
  memberId: number;
  deviceId: string;
  status: 'online' | 'offline' | 'away';
  lastSeen: number;
  connectedAt: number;
}

export interface PresenceEvent {
  memberId: number;
  status: 'online' | 'offline' | 'away';
  lastSeen?: number;
  timestamp: string;
  devices: number;
  activeDevices: number;
}

export interface MemberStatus {
  memberId: number;
  status: 'online' | 'offline' | 'away';
  lastSeen?: number;
  devices: string[];
  activeDevices: number;
}

export interface DeviceInfo {
  devices: string[];
  activeDevices: string[];
}
