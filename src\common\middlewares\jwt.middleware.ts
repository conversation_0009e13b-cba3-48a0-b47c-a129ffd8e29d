import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtMiddleware implements NestMiddleware {
  private readonly logger = new Logger(JwtMiddleware.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  use(req: Request, res: Response, next: NextFunction) {
    const token = this.extractToken(req);

    if (token) {
      try {
        const decoded = this.jwtService.verify(token, {
          secret: this.configService.get<string>('jwt.secret'),
        });

        req['user'] = decoded;
        req['tokenType'] = 'access';
      } catch (error) {
        this.logger.warn(`JWT verification failed: ${error.message}`);
        // Let guards handle it, so do not throw
      }
    }

    next();
  }

  private extractToken(req: Request): string | undefined {
    const authHeader = req.headers.authorization;
    const cookieToken = req.cookies?.access_token;
    const customHeaderToken = req.headers['x-access-token'];

    if (authHeader?.startsWith('Bearer ')) {
      return authHeader.slice(7);
    }

    if (typeof cookieToken === 'string') {
      return cookieToken;
    }

    if (typeof customHeaderToken === 'string') {
      return customHeaderToken;
    }

    return undefined;
  }
}
