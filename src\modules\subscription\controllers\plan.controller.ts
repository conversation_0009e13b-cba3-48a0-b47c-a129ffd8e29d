import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PlanService } from '../services/plan.service';

@ApiTags('Plans')
@Controller('plans')
export class PlanController {
  constructor(private readonly planService: PlanService) {}

  @Get()
  @ApiOperation({ summary: 'Get all subscription plans' })
  @ApiResponse({
    status: 200,
    description: 'Subscription plans retrieved successfully',
  })
  async findAll(@Query() query: any) {
    return this.planService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get subscription plan by ID' })
  @ApiResponse({
    status: 200,
    description: 'Subscription plan retrieved successfully',
  })
  async findOne(@Param('id') id: string) {
    return this.planService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create new subscription plan' })
  @ApiResponse({
    status: 201,
    description: 'Subscription plan created successfully',
  })
  async create(@Body() createPlanDto: any) {
    return this.planService.create(createPlanDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update subscription plan' })
  @ApiResponse({
    status: 200,
    description: 'Subscription plan updated successfully',
  })
  async update(@Param('id') id: string, @Body() updatePlanDto: any) {
    return this.planService.update(id, updatePlanDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete subscription plan' })
  @ApiResponse({
    status: 200,
    description: 'Subscription plan deleted successfully',
  })
  async remove(@Param('id') id: string) {
    return this.planService.remove(id);
  }

  @Get('active')
  @ApiOperation({ summary: 'Get all active subscription plans' })
  @ApiResponse({
    status: 200,
    description: 'Active subscription plans retrieved successfully',
  })
  async findActive(@Query() query: any) {
    return this.planService.findActive(query);
  }

  @Post(':id/activate')
  @ApiOperation({ summary: 'Activate subscription plan' })
  @ApiResponse({
    status: 200,
    description: 'Subscription plan activated successfully',
  })
  async activate(@Param('id') id: string) {
    return this.planService.activate(id);
  }

  @Post(':id/deactivate')
  @ApiOperation({ summary: 'Deactivate subscription plan' })
  @ApiResponse({
    status: 200,
    description: 'Subscription plan deactivated successfully',
  })
  async deactivate(@Param('id') id: string) {
    return this.planService.deactivate(id);
  }
}
