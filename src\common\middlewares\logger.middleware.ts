import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  private readonly logger = new Logger(LoggerMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    const { method, originalUrl } = req;
    const user = req['user']?.email || req['user']?.id || 'Guest';
    const ip = req.ip;

    const start = Date.now();

    res.on('finish', () => {
      const statusCode = res.statusCode;
      const duration = Date.now() - start;
      this.logger.log(
        `[${method}] ${originalUrl} - ${statusCode} - ${duration}ms - ${user} - IP: ${ip}`,
      );
    });

    next();
  }
}
