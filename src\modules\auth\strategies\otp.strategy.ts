import {
  Injectable,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { OrgMember } from '../../members/entities/org-member.entity';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { OtpVerification } from '../../auth/entities/otp-verification.entity';
import { Not, IsNull } from 'typeorm';
import {
  OTP_VERIFICATION_REQUIRED,
  OTP_EXPIRED,
  OTP_NOT_FOUND,
  OTP_INVALID_TYPE,
} from '../../../common/constants/otp.constants';

@Injectable()
export class OtpStrategy extends PassportStrategy(Strategy, 'otp') {
  constructor(
    private configService: ConfigService,
    @InjectRepository(OrgMember)
    private readonly orgMemberRepo: Repository<OrgMember>,
    @InjectRepository(OtpVerification)
    private readonly otpVerificationRepo: Repository<OtpVerification>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('jwt.secret'),
    });
  }

  async validate(payload: any): Promise<OrgMember> {
    const userId = payload.sub;
    const userType = payload.type;

    if (userType !== 'org_member') {
      throw new UnauthorizedException(OTP_INVALID_TYPE);
    }

    // Fetch the organization member
    const member = await this.orgMemberRepo.findOne({
      where: { id: userId },
    });

    if (!member) {
      throw new UnauthorizedException(OTP_NOT_FOUND);
    }

    // Check if the user has a valid OTP verification
    const latestVerifiedOtp = await this.otpVerificationRepo.findOne({
      where: {
        memberId: userId,
        verifiedAt: Not(IsNull()),
      },
      order: {
        verifiedAt: 'DESC',
      },
    });

    if (!latestVerifiedOtp) {
      throw new ForbiddenException(OTP_VERIFICATION_REQUIRED);
    }

    // Optional: Check if the OTP verification is still valid (not expired)
    const now = new Date();
    if (latestVerifiedOtp.expiresAt < now) {
      throw new ForbiddenException(OTP_EXPIRED);
    }

    // If OTP is valid and user is an org member, return the member
    return member;
  }
}
