import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, LessThanOrEqual, MoreThan, Repository } from 'typeorm';
import { GroupMember } from '../entities/group-member.entity';
import { Group } from '../entities/group.entity';
import { Message } from '../../../modules/messages/entities/message.entity';
import { OrgMember } from '../../../modules/members/entities/org-member.entity';
import { GroupEncryptionKey } from '../../../modules/security/entities/group-encryption-keys.entity';
import { plainToInstance } from 'class-transformer';
import { MessagesService } from '../../../modules/messages/services/messages.service';

@Injectable()
export class GroupMembersService {
  constructor(
    @InjectRepository(GroupMember)
    private readonly groupMemberRepo: Repository<GroupMember>,
    @InjectRepository(Group)
    private readonly groupRepo: Repository<Group>,
    @InjectRepository(OrgMember)
    private readonly orgMemberRepo: Repository<OrgMember>,
    @Inject(forwardRef(() => MessagesService))
    private readonly messagesService: MessagesService,
    @InjectRepository(GroupEncryptionKey)
    private readonly groupEncryptionKeyRepository: Repository<GroupEncryptionKey>,
  ) {}

  async getUserGroups(memberId: number) {
    // Find all active groups where the user is a member
    const groupMemberships = await this.groupMemberRepo.find({
      where: { memberId, isActive: true },
      relations: ['group'],
    });

    if (groupMemberships.length === 0) return [];

    const groupsData = await Promise.all(
      groupMemberships.map(async (membership) => {
        const group = await this.groupRepo.findOne({
          where: { id: membership.groupId, isActive: true },
          relations: ['creator'],
        });

        if (!group) return null;

        const joinedAt = membership.joinedAt;

        // Get the last message after joinedAt
        const lastMessage = await this.messagesService.getLastMessageInGroup(
          membership.groupId,
          joinedAt,
        );

        const encryptionKeyEntry =
          await this.groupEncryptionKeyRepository.findOne({
            where: {
              groupId: membership.groupId,
              memberId,
              keyVersion: group.currentKeyVersion,
              isActive: true,
            },
          });

        const encryptedGroupKey = encryptionKeyEntry?.encryptedGroupKey || null;

        const groupMembers = await this.groupMemberRepo.find({
          where: { groupId: membership.groupId, isActive: true },
          relations: ['member'],
        });

        const members = await Promise.all(
          groupMembers.map(async (gm) => {
            const member = await this.orgMemberRepo.findOne({
              where: { id: gm.memberId },
            });
            if (!member) return null;
            return {
              id: member.id,
              name: member.name,
              imageUrl: member.imageUrl,
            };
          }),
        );

        const admin = group.creator?.username;

        // Filter unread count by joinedAt as well (optional but recommended)
        const totalMessages =
          await this.messagesService.getTotalMessagesInGroup(
            membership.groupId,
            joinedAt,
          );
        const readMessages =
          await this.messagesService.countGroupMessagesReadByMember(
            membership.groupId,
            memberId,
          );

        const unreadCount = totalMessages - readMessages;

        return {
          id: group.id,
          type: 'group',
          name: group.name,
          encryptedGroupKey,
          date:
            lastMessage?.sentAt.toISOString() || group.createdAt.toISOString(),
          imageUrl: group.imageUrl,
          msg: lastMessage ? lastMessage.content : '',
          msgNonce:
            (lastMessage && 'nonce' in lastMessage
              ? (lastMessage as any).nonce
              : '') || '',
          msgGroupKeyVersion:
            (lastMessage && 'groupKeyVersion' in lastMessage
              ? (lastMessage as any).groupKeyVersion
              : '') || '',
          read: unreadCount === 0,
          unreadCount: unreadCount,
          members: members,
          lastSender: lastMessage?.sender?.name || null,
          lastSenderID: lastMessage?.sender?.id || null,
          createdAt: group.createdAt.toISOString(),
          admin: admin,
        };
      }),
    );

    return groupsData.filter((group) => group !== null);
  }

  async getGroupMessagesWithKey(groupId: number, memberId: number) {
    // Fetch group with members
    const group = await this.groupRepo.findOne({
      where: { id: groupId },
      relations: ['members', 'members.member'],
    });
    if (!group) throw new NotFoundException('Group not found');

    const groupMembership = await this.groupMemberRepo.findOne({
      where: { groupId, memberId, isActive: true },
    });

    if (!groupMembership) throw new Error('Member not part of this group');

    const joinedAt = groupMembership.joinedAt;

    // Fetch messages as before
    const messages = await this.messagesService.getMessagesInGroup(
      groupId,
      joinedAt,
    );

    // Fetch the encrypted group key for this member and currentKeyVersion
    const encryptionKeyEntry = await this.groupEncryptionKeyRepository.findOne({
      where: {
        groupId,
        memberId,
        keyVersion: group.currentKeyVersion,
        isActive: true,
      },
    });

    const encryptedGroupKey = encryptionKeyEntry?.encryptedGroupKey || null;

    // Prepare members list as before
    const groupMembers = group.members
      .filter((m) => m.isActive)
      .map((gm) => ({
        id: gm.member.id,
        name: gm.member.name,
        imageUrl: gm.member.imageUrl,
        joinedAt: gm.joinedAt?.toISOString(),
        isMute: gm.isMute,
      }));

    const groupDetails = {
      groupId: group.id,
      groupName: group.name,
      imageUrl: group.imageUrl,
      currentKeyVersion: group.currentKeyVersion,
      encryptedGroupKey,
      createdAt: group.createdAt,
      lastUpdated: group.updatedAt,
      groupMembers,
      messages: messages.map((msg) => {
        return {
          id: msg.id,
          groupId: msg.groupId,
          groupKeyVersion: msg.groupKeyVersion,
          from: {
            id: msg.sender?.id,
            name: msg.sender?.name,
            imageUrl: msg.sender?.imageUrl,
          },
          visibilities: (msg as any).visibilities
            ? (msg as any).visibilities.map((v: any) => ({
                memberId: v.memberId,
                isVisible: v.isVisible,
              }))
            : [],

          date: msg.date?.toISOString(),
          msg: msg?.content,
          nonce: (msg as any)?.nonce,
          file: msg?.file
            ? {
                id: msg.file.id,
                fileUrl: msg.file.fileUrl,
                fileName: msg.file.filename,
                type: msg.file.fileType,
                size: msg.file.fileSize,
                metaData: msg.file.metaData,
              }
            : null,
          repliedMessage: (msg as any).replyTo
            ? {
                id: (msg as any).replyTo.id,
                text: (msg as any).replyTo.encryptedContent,
                nonce: (msg as any).replyTo?.nonce,
                replyGroupKeyVersion: (msg as any).replyTo.groupKeyVersion,
                createdAt: (msg as any).replyTo.sentAt?.toISOString(),
                user: {
                  id: (msg as any).replyTo.sender?.id,
                  name: (msg as any).replyTo.sender?.name,
                  imageUrl: (msg as any).replyTo.sender?.imageUrl,
                },
              }
            : null,
          readBy: (msg as any)?.reads
            ? (msg as any).reads.map((reader: any) => ({
                id: reader.readerId,
              }))
            : [],
        };
      }),
    };

    return plainToInstance(Message, groupDetails, {
      excludeExtraneousValues: false,
    });
  }

  async getAllGroupEncryptionKeysForMember(memberId: number) {
    const encryptionKeys = await this.groupEncryptionKeyRepository.find({
      where: { memberId },
      order: { groupId: 'ASC', keyVersion: 'ASC' },
    });

    // Group keys by groupId into an array of objects
    const grouped = new Map<number, any[]>();

    for (const key of encryptionKeys) {
      if (!grouped.has(key.groupId)) {
        grouped.set(key.groupId, []);
      }

      grouped.get(key.groupId)!.push({
        keyVersion: key.keyVersion,
        encryptedGroupKey: key.encryptedGroupKey,
        isActive: key.isActive,
      });
    }

    // Transform Map into array
    const result = Array.from(grouped.entries()).map(([groupId, keys]) => ({
      groupId,
      encryptionKeys: keys,
    }));

    return result;
  }

  async getGroupMemberDetails(groupId: number) {
    const group = await this.groupRepo.findOne({
      where: { id: groupId },
      relations: ['members', 'members.member'],
    });
    if (!group) throw new Error('Group not found');

    const groupMembers = group.members
      .filter((m) => m.isActive)
      .map((gm) => ({
        id: gm.member.id,
        name: gm.member.name,
        imageUrl: gm.member.imageUrl,
        joinedAt: gm.joinedAt?.toISOString(),
        isMute: gm.isMute,
        groupMemberId: gm.id,
        phoneNo: gm.member.phoneNo || null,
      }));

    return {
      groupId: group.id,
      groupName: group.name,
      groupImageUrl: group.imageUrl,
      members: groupMembers,
    };
  }

  async findGroupMember(groupId: number, memberId: number) {
    return this.groupMemberRepo.findOne({
      where: { groupId, memberId },
      relations: ['group', 'member'], // optional: add related entities
    });
  }

  async getGroupMembers(groupId: number) {
    const groupMembers = await this.groupMemberRepo.find({
      where: { groupId, isActive: true },
      relations: ['member'],
    });

    return groupMembers.map((gm) => ({
      id: gm.member.id,
      name: gm.member.name,
      imageUrl: gm.member.imageUrl,
      joinedAt: gm.joinedAt?.toISOString(),
      isMute: gm.isMute,
    }));
  }

  async getUserGroupsSinceLastOffline(memberId: number, lastOffline: Date) {
    const now = new Date();
    const groupMemberships = await this.groupMemberRepo.find({
      where: { memberId, isActive: true },
      relations: ['group'],
    });

    if (groupMemberships.length === 0) return [];

    const result = await Promise.all(
      groupMemberships.map(async (membership) => {
        const group = membership.group;
        const joinedAt = membership.joinedAt;

        // Was the user added after going offline?
        const isNewGroup = joinedAt > lastOffline;

        // Get messages sent after lastOffline
        const missedMessages =
          await this.messagesService.getMessagesInGroupBetween(
            group.id,
            lastOffline,
            now,
          );

        const { joinedMembers, leftMembers } =
          await this.getMembershipChangesFromEncryptionKeys(
            group.id,
            lastOffline,
            now,
          );

        if (
          !isNewGroup &&
          missedMessages.length === 0 &&
          joinedMembers.length === 0 &&
          leftMembers.length === 0
        )
          return null;

        // Optional: pick the latest message in missed messages
        const lastMissedMessage = missedMessages[0]; // Because it's DESC order

        return {
          groupId: group.id,
          name: group.name,
          imageUrl: group.imageUrl,
          newGroup: isNewGroup,
          missedUnreadCount: missedMessages.length,
          missedLastMessage: lastMissedMessage
            ? {
                id: lastMissedMessage.id,
                encryptedContent: lastMissedMessage.content,
                sentAt: lastMissedMessage.sentAt.toISOString(),
                nonce: lastMissedMessage.nonce,
                groupKeyVersion: lastMissedMessage.groupKeyVersion,
                sender: {
                  id: lastMissedMessage.sender?.id || null,
                  name: lastMissedMessage.sender?.name || null,
                },
              }
            : null,
          membershipChanges: {
            joined: joinedMembers,
            left: leftMembers,
          },
        };
      }),
    );

    return result.filter((g) => g !== null);
  }

  async getUpdatedGroupEncryptionKeysForMember(
    memberId: number,
    lastOffline: Date,
  ) {
    const now = new Date();

    // 1. Fetch keys updated after lastOffline for this member
    const updatedKeys = await this.groupEncryptionKeyRepository.find({
      where: {
        memberId,
        updatedAt: Between(lastOffline, now),
      },
      order: { groupId: 'ASC', keyVersion: 'ASC' },
    });

    // 2. Group by groupId
    const grouped = new Map<number, any[]>();

    for (const key of updatedKeys) {
      if (!grouped.has(key.groupId)) {
        grouped.set(key.groupId, []);
      }

      grouped.get(key.groupId)!.push({
        keyVersion: key.keyVersion,
        encryptedGroupKey: key.encryptedGroupKey,
        isActive: key.isActive,
        updatedAt: key.updatedAt?.toISOString(),
      });
    }

    // 3. Format output
    const result = Array.from(grouped.entries()).map(([groupId, keys]) => ({
      groupId,
      encryptionKeys: keys,
    }));

    return result;
  }

  async getMembershipChangesFromEncryptionKeys(
    groupId: number,
    lastOffline: Date,
    currentTime: Date,
  ): Promise<{
    joinedMembers: { memberId: number; name: string }[];
    leftMembers: { memberId: number; name: string }[];
  }> {
    // 1. Get all active keys at each point
    const previousKeys = await this.groupEncryptionKeyRepository.find({
      where: {
        groupId,
        createdAt: LessThanOrEqual(lastOffline),
        isActive: true,
      },
      relations: ['member'],
    });

    const currentKeys = await this.groupEncryptionKeyRepository.find({
      where: {
        groupId,
        createdAt: LessThanOrEqual(currentTime),
        isActive: true,
      },
      relations: ['member'],
    });

    // 2. Build member sets
    const pastMemberIds = new Set(previousKeys.map((k) => k.memberId));
    const currentMemberIds = new Set(currentKeys.map((k) => k.memberId));

    // 3. Compare
    const joined = [...currentMemberIds].filter((id) => !pastMemberIds.has(id));
    const left = [...pastMemberIds].filter((id) => !currentMemberIds.has(id));

    const joinedMembers = currentKeys
      .filter((k) => joined.includes(k.memberId))
      .map((k) => ({
        memberId: k.memberId,
        name: k.member?.name || `User ${k.memberId}`,
      }));

    const leftMembers = previousKeys
      .filter((k) => left.includes(k.memberId))
      .map((k) => ({
        memberId: k.memberId,
        name: k.member?.name || `User ${k.memberId}`,
      }));

    return {
      joinedMembers,
      leftMembers,
    };
  }

  async getActiveMembersWithDetails(groupId: number) {
    return this.groupMemberRepo.find({
      where: { groupId, isActive: true },
      relations: ['member'],
    });
  }

  async syncGroupMessagesSinceLastOffline(
    groupId: number,
    memberId: number,
    lastOfflineRaw: Date | string,
  ) {
    const now = new Date();
    const lastOffline = new Date(lastOfflineRaw);

    const group = await this.groupRepo.findOne({
      where: { id: groupId },
      relations: ['members', 'members.member'],
    });

    if (!group) throw new NotFoundException('Group not found');

    const groupMemberships = await this.groupMemberRepo.find({
      where: { memberId, isActive: true },
      relations: ['group'],
    });

    if (!groupMemberships || groupMemberships.length === 0) {
      throw new Error('Member not part of this group');
    }

    // 2. Get messages [lastOffline → now], excluding sender = memberId
    const visibleMessages =
      await this.messagesService.getMessagesInGroupBetween(
        groupId,
        lastOffline,
        now,
      );

    const filteredMessages = (visibleMessages || []).filter(
      (msg) => msg.senderId !== memberId,
    );

    // 3. Get deleted messages in same window, also excluding sender
    const deletedMessages = await this.messagesService.getDeletedMessagesSince(
      groupId,
      lastOffline,
    );

    const filteredDeletedMessages = (deletedMessages || []).filter(
      (msg) => msg.senderId !== memberId,
    );

    // 4. Membership changes
    const { joinedMembers = [], leftMembers = [] } =
      (await this.getMembershipChangesFromEncryptionKeys(
        group.id,
        lastOffline,
        now,
      )) || {};

    // 5. Encrypted group key
    const encryptionKeyEntry = await this.groupEncryptionKeyRepository.findOne({
      where: {
        groupId,
        memberId,
        keyVersion: group.currentKeyVersion,
        isActive: true,
      },
    });

    const encryptedGroupKey = encryptionKeyEntry?.encryptedGroupKey || null;

    // 6. Active members
    const groupMembers = await this.getActiveMembersWithDetails(groupId);

    const members = (groupMembers || []).map((gm) => ({
      id: gm.member.id,
      name: gm.member.name,
      imageUrl: gm.member.imageUrl,
      joinedAt: gm.joinedAt?.toISOString(),
      isMute: gm.isMute,
    }));

    // 7. Return formatted response
    return {
      groupId: group.id,
      groupName: group.name,
      imageUrl: group.imageUrl,
      currentKeyVersion: group.currentKeyVersion,
      encryptedGroupKey,
      createdAt: group.createdAt,
      lastUpdated: group.updatedAt,
      groupMembers: members,
      messages: (filteredMessages || []).map((msg) => ({
        id: msg.id,
        groupId: msg.groupId,
        groupKeyVersion: msg.groupKeyVersion,
        from: {
          id: msg.sender?.id,
          name: msg.sender?.name,
          imageUrl: msg.sender?.imageUrl,
        },
        visibilities:
          (msg as any).visibilities?.map((v: any) => ({
            memberId: v.memberId,
            isVisible: v.isVisible,
          })) || [],
        date: msg.sentAt?.toISOString(),
        msg: msg?.content,
        nonce: (msg as any)?.nonce,
        file: msg?.file
          ? {
              id: msg.file.id,
              fileUrl: msg.file.fileUrl,
              fileName: msg.file.filename,
              type: msg.file.fileType,
              size: msg.file.fileSize,
              metaData: msg.file.metaData,
            }
          : null,
        repliedMessage: (msg as any).replyTo
          ? {
              id: (msg as any).replyTo.id,
              text: (msg as any).replyTo.encryptedContent,
              nonce: (msg as any).replyTo?.nonce,
              replyGroupKeyVersion: (msg as any).replyTo.groupKeyVersion,
              createdAt: (msg as any).replyTo.sentAt?.toISOString(),
              user: {
                id: (msg as any).replyTo.sender?.id,
                name: (msg as any).replyTo.sender?.name,
                imageUrl: (msg as any).replyTo.sender?.imageUrl,
              },
            }
          : null,
        readBy:
          (msg as any)?.reads?.map((r: any) => ({ id: r.readerId })) || [],
      })),
      membershipChanges: {
        joined: joinedMembers,
        left: leftMembers,
      },
      deletedMessageIds: (filteredDeletedMessages || []).map((m) => m.id),
    };
  }

  async getMembershipStatus(
    groupId: number,
    memberId: number,
  ): Promise<'joined' | 'left' | 'not found'> {
    const record = await this.groupMemberRepo.findOne({
      where: {
        groupId,
        memberId,
      },
    });

    if (!record) {
      return 'not found';
    }

    return record.leftAt ? 'left' : 'joined';
  }
}
