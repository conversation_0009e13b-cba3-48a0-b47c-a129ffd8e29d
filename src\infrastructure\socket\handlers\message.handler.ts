import { Injectable } from '@nestjs/common';
import { MessageProducer } from '../../rabbitmq/producers/message.producer';
import { SendMessageDto } from '../../../modules/messages/dto/send-message.dto';

@Injectable()
export class MessageHandler {
  constructor(private readonly messageProducer: MessageProducer) {}

  async handleSendMessage(dto: SendMessageDto): Promise<void> {
    // Optionally add validations here
    await this.messageProducer.sendMessageToQueue(dto);
  }
}
