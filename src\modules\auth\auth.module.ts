import { Module } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PassportModule } from '@nestjs/passport';
import { AuthController } from './controllers/auth.controller';
import { AuthService } from './services/auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { RedisModule } from '../../infrastructure/redis/redis.module';
import { OtpVerification } from './entities';
import { OtpStrategy } from './strategies/otp.strategy';
import { MembersModule } from '../members/members.module';
import { OtpController } from './controllers/otp.controller';
import { OtpService } from './services/otp.service';
import { QueueModule } from '../../infrastructure/queue/queue.module';
import { UserModule } from '../users/users.module';
import { OrganizationsModule } from '../organization/organizations.module';
import { CoreModule } from 'src/core/core.module';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    TypeOrmModule.forFeature([OtpVerification]),
    RedisModule,
    MembersModule,
    CoreModule,
    QueueModule,
    UserModule,
    OrganizationsModule,
  ],
  controllers: [AuthController, OtpController],
  providers: [AuthService, JwtStrategy, OtpStrategy, JwtService, OtpService],
  exports: [AuthService, JwtStrategy, OtpService],
})
export class AuthModule {}
