import { <PERSON>E<PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsString } from 'class-validator';

export class AdminResponseDto {
  @IsNumber()
  id: number;

  @IsString()
  username: string;

  @IsString()
  email: string;

  @IsEnum(['product_admin', 'org_admin'])
  type: 'product_admin' | 'org_admin';

  @IsOptional()
  @IsNumber()
  orgId?: number;

  @IsOptional()
  @IsString()
  orgName?: string;

  @IsNumber()
  roleId: number;

  @IsOptional()
  @IsString()
  imageUrl?: string;

  @IsOptional()
  @IsString()
  encryptedAdminSecretKey?: string;

  @IsOptional()
  @IsString()
  adminSecretKeyNonce?: string;

  @IsOptional()
  @IsString()
  adminSecretKeySalt?: string;
}

export class LoginResponseDto {
  @IsString()
  accessToken: string;

  @IsString()
  refreshToken: string;
}
