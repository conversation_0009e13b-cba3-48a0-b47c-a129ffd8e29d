/**
 * Metadata key for OTP verification requirement
 */
export const REQUIRE_OTP_KEY = 'requireOtp';
export const OTP_VERIFICATION_REQUIRED = 'OTP_VERIFICATION_REQUIRED';
export const OTP_EXPIRED = 'OTP_EXPIRED';
export const INVALID_OTP = 'INVALID_OTP';
export const OTP_NOT_FOUND = 'OTP_NOT_FOUND';
export const OTP_ALREADY_VERIFIED = 'OTP_ALREADY_VERIFIED';
export const OTP_INVALID_TYPE = 'OTP_INVALID_TYPE';
export const MAX_OTP_ATTEMPTS = 3;
