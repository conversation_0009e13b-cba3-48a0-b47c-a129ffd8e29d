import { Module } from '@nestjs/common';
import { NotificationModule } from './notification/notification.module';
import { QueueModule } from './queue/queue.module';
import { RabbitMQConfigModule } from './rabbitmq/rabbitMq.module';
import { RedisModule } from './redis/redis.module';
import { SocketModule } from './socket/socket.module';

@Module({
  imports: [
    NotificationModule,
    QueueModule,
    RabbitMQConfigModule,
    RedisModule,
    SocketModule,
  ],
  exports: [
    NotificationModule,
    QueueModule,
    RabbitMQConfigModule,
    RedisModule,
    SocketModule,
  ],
})
export class InfrastructureModule {}
