import { randomBytes } from 'crypto';
// src/utils/credentials.util.ts

/**
 * Generates a username from an email address
 * @param email The email address to generate a username from
 * @returns A username derived from the email address
 */
export function generateUsername(email: string): string {
  // Take the part before @ and remove special characters
  const baseUsername = email
    .split('@')[0]
    .replace(/[^a-zA-Z0-9]/g, '')
    .toLowerCase();

  return baseUsername;
}

/**
 * Generates a secure random password
 * @param length The length of the password, defaults to 10
 * @returns A random password
 */

export function generatePassword(length: number = 10): string {
  const minLength = 6;
  const actualLength = Math.max(length, minLength);

  const uppercaseChars = 'ABCDEFGHJKLMNPQRSTUVWXYZ';
  const lowercaseChars = 'abcdefghijkmnopqrstuvwxyz';
  const numberChars = '23456789';
  const specialChars = '!@#$%^&*()_+[]{}|;:,.<>?';

  const allChars = uppercaseChars + lowercaseChars + numberChars + specialChars;

  // Start with one char from each category
  let password =
    getSecureRandomChar(uppercaseChars) +
    getSecureRandomChar(lowercaseChars) +
    getSecureRandomChar(numberChars) +
    getSecureRandomChar(specialChars);

  // Fill the rest with random chars from full charset
  for (let i = 4; i < actualLength; i++) {
    password += getSecureRandomChar(allChars);
  }

  return shuffleString(password);
}

/**
 * Gets a random character from a string
 * @param characters The string to get a character from
 * @returns A random character from the string
 */
function getSecureRandomChar(charset: string): string {
  const byte = randomBytes(1)[0];
  return charset.charAt(byte % charset.length);
}

/**
 * Shuffles a string
 * @param str The string to shuffle
 * @returns The shuffled string
 */
function shuffleString(str: string): string {
  const array = str.split('');
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor((randomBytes(1)[0] / 256) * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array.join('');
}
