import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MediaService } from './services/media.service';
import { MediaUploadService } from './services/media-upload.service';
import { MediaDownloadService } from './services/media-download.service';
import { MediaStreamingService } from './services/media-streaming.service';
import { MediaCompressionService } from './services/media-compression.service';
import { MediaEncryptionService } from './services/media-encryption.service';
import { FfmpegService } from './services/ffmpeg.service';
import { ThumbnailService } from './services/thumbnail.service';
import { WaveformService } from './services/waveform.service';
import { MediaController } from './controllers/media.controller';
import { MediaDownloadController } from './controllers/media-download.controller';
import { MediaStreamingController } from './controllers/media-streaming.controller';
import { WaveformController } from './controllers/waveform.controller';

/**
 * Media Module
 *
 * Handles media file management including upload, download,
 * streaming, compression, encryption, thumbnail generation,
 * and audio waveform processing with visualization.
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Add media-related entities here when created
    ]),
  ],
  controllers: [
    MediaController,
    MediaDownloadController,
    MediaStreamingController,
    WaveformController,
  ],
  providers: [
    MediaService,
    MediaUploadService,
    MediaDownloadService,
    MediaStreamingService,
    MediaCompressionService,
    MediaEncryptionService,
    FfmpegService,
    ThumbnailService,
    WaveformService,
  ],
  exports: [
    MediaService,
    MediaUploadService,
    MediaDownloadService,
    MediaStreamingService,
    MediaCompressionService,
    MediaEncryptionService,
    FfmpegService,
    ThumbnailService,
    WaveformService,
  ],
})
export class MediaModule {}
