import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Group } from '../../groups/entities/group.entity';

@Entity('group_member_logs')
export class GroupMemberLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'group_id' })
  groupId: number;

  @Column({ name: 'member_id' })
  memberId: number;

  @Column({ name: 'action' })
  action: 'join' | 'leave' | 'mute' | 'unmute';

  @Column({ name: 'member_seq', type: 'bigint' })
  memberSeq: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => Group, (group) => group.members)
  @JoinColumn({ name: 'group_id' })
  group: Group;
}
