import { IsE<PERSON>, <PERSON>NotEmpty, <PERSON>Optional, IsString } from 'class-validator';
import { DeviceType } from '../entities/member-fcm-token.entity';

export class CreateFcmTokenDto {
  @IsNotEmpty()
  @IsString()
  fcmToken: string;

  @IsOptional()
  @IsString()
  deviceId?: string;

  @IsOptional()
  @IsEnum(DeviceType)
  deviceType?: DeviceType;

  @IsOptional()
  @IsString()
  appVersion?: string;
}
