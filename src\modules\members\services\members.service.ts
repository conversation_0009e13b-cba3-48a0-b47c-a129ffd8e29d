import {
  Injectable,
  ConflictException,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  Inject,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository, IsNull } from 'typeorm';
import { MemberFcmToken } from '../entities/member-fcm-token.entity';
import { OrgMember } from '../entities/org-member.entity';
import { CreateMemberDto } from '../dto/create-member.dto';
import { EncryptionService } from '../../../core/encryption/encryption.service';
import { UpdateMemberDto } from '../dto/update-member.dto';
import { CreateFcmTokenDto } from '../dto/create-fcm.dto';
import { plainToInstance } from 'class-transformer';
import { UsersService } from '../../users/services/users.service';
import { OrganizationsService } from '../../organization/services/organizations.service';

@Injectable()
export class MembersService {
  constructor(
    @InjectRepository(OrgMember)
    private readonly memberRepository: Repository<OrgMember>,
    @InjectRepository(MemberFcmToken)
    private readonly fcmTokenRepository: Repository<MemberFcmToken>,
    private readonly encryptionService: EncryptionService,
    @Inject(UsersService)
    private readonly userService: UsersService,
    @Inject(OrganizationsService)
    private readonly organizationService: OrganizationsService,
  ) {}

  async create(createMemberDto: CreateMemberDto, userId): Promise<OrgMember> {
    // Check if member with email already exists
    const existingMember = await this.memberRepository.findOne({
      where: [
        { email: createMemberDto.email },
        { phoneNo: createMemberDto.phoneNo },
      ],
    });

    if (existingMember) {
      throw new BadRequestException(
        'A member with this email or phone number already exists',
      );
    }
    const existingUser = await this.userService.findByMail(
      createMemberDto.email,
    );
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    const user = await this.userService.findById(userId);

    if (user.roleId === 1) {
      throw new BadRequestException(
        'Product admin is not allowed to create organization members',
      );
    }

    // Verify that the organization exists
    const organization = await this.organizationService.findOne(user.orgId);

    if (!organization) {
      throw new NotFoundException(
        `Organization with ID ${user.orgId} not found`,
      );
    }

    const { publicKey, secretKey } =
      await this.encryptionService.generateKeyPair();

    // Encrypt the private key before saving
    const encryptedPrivateKey = await this.encryptionService.encryptSecretKey(
      secretKey,
      createMemberDto.phoneNo,
    );

    // Create the member
    const member = this.memberRepository.create({
      name: createMemberDto.name,
      phoneNo: createMemberDto.phoneNo,
      email: createMemberDto.email,
      imageUrl: createMemberDto.fileUrl,
      orgId: user.orgId,
      publicKey,
      encryptedPrivateKey,
      keyCreatedAt: new Date(),
    });

    const savedMember = await this.memberRepository.save(member);

    return savedMember;
  }
  async invalidToken(memberId: number, deviceId: string) {
    await this.fcmTokenRepository.update(
      { memberId, deviceId },
      { isActive: false, deletedAt: new Date() },
    );
  }

  async upsertFcmToken(
    queryRunner: any,
    memberId: number,
    dto: CreateFcmTokenDto,
  ) {
    await queryRunner.manager.upsert(
      MemberFcmToken,
      {
        memberId: memberId,
        deviceId: dto.deviceId,
        fcmToken: dto.fcmToken,
        deviceType: dto.deviceType,
        isActive: true,
        lastUsedAt: new Date(),
      },
      {
        conflictPaths: ['deviceId', 'memberId'],
        skipUpdateIfNoValuesChanged: false,
      },
    );
  }

  async findByPhoneNo(phoneNo: string): Promise<OrgMember> {
    const member = await this.memberRepository.findOne({
      where: { phoneNo },
    });
    if (!member) {
      throw new NotFoundException(
        `Member with phone number ${phoneNo} not found`,
      );
    }
    return member;
  }

  async findAll(orgId: number): Promise<OrgMember[]> {
    const members = await this.memberRepository.find({
      where: { orgId },
    });

    return plainToInstance(OrgMember, members, {
      excludeExtraneousValues: false,
    });
  }

  async findOne(id: number): Promise<OrgMember> {
    const member = await this.memberRepository.findOne({
      where: { id },
      relations: ['organization', 'groupMemberships', 'fcmTokens'],
    });

    if (!member) {
      throw new NotFoundException(`Member with ID ${id} not found`);
    }

    return member;
  }

  async update(
    id: number,
    updateMemberDto: UpdateMemberDto,
    user,
  ): Promise<OrgMember> {
    const member = await this.memberRepository.findOne({ where: { id } });

    if (!member) {
      throw new NotFoundException(`Member with ID ${id} not found`);
    }

    if (user.orgId !== member.orgId) {
      throw new UnauthorizedException();
    }

    // Check if phoneNo changed
    const isPhoneChanged =
      updateMemberDto.phoneNo && updateMemberDto.phoneNo !== member.phoneNo;

    let newEncryptedPrivateKey = member.encryptedPrivateKey;

    if (isPhoneChanged) {
      // 1. Decrypt existing private key using old phoneNo
      let decryptedPrivateKey: string;
      try {
        decryptedPrivateKey = this.encryptionService.decryptSecretKey(
          JSON.parse(member.encryptedPrivateKey),
          member.phoneNo,
        );
      } catch (err) {
        throw new BadRequestException('Failed to decrypt existing private key');
      }

      newEncryptedPrivateKey = await this.encryptionService.encryptSecretKey(
        decryptedPrivateKey,
        updateMemberDto.phoneNo,
      );
    }

    await this.memberRepository.update(id, {
      name: updateMemberDto.name,
      imageUrl: updateMemberDto.fileUrl,
      email: updateMemberDto.email || member.email,
      phoneNo: updateMemberDto.phoneNo || member.phoneNo,
      encryptedPrivateKey: newEncryptedPrivateKey,
    });

    const updatedMember = await this.memberRepository.findOne({
      where: { id },
    });

    if (!updatedMember) {
      throw new NotFoundException(
        `Member with ID ${id} not found after update`,
      );
    }

    return updatedMember;
  }

  async findActiveFcmTokens(memberIds: number[]): Promise<string[]> {
    if (!memberIds || memberIds.length === 0) return [];

    const tokens = await this.fcmTokenRepository.find({
      where: {
        memberId: In(memberIds),
        isActive: true,
        deletedAt: IsNull(),
      },
    });

    return tokens.map((token) => token.fcmToken);
  }

  async createFcmToken(
    memberId: number,
    dto: CreateFcmTokenDto,
  ): Promise<MemberFcmToken> {
    const member = await this.memberRepository.findOne({
      where: { id: memberId },
    });

    if (!member) {
      throw new NotFoundException(`Member with ID ${memberId} not found`);
    }

    // Deactivate old tokens (optional, if single-token policy)
    await this.fcmTokenRepository.update(
      { fcmToken: dto.fcmToken },
      { isActive: false, deletedAt: new Date() },
    );

    const newToken = this.fcmTokenRepository.create({
      memberId,
      fcmToken: dto.fcmToken,
      deviceId: dto.deviceId,
      deviceType: dto.deviceType,
      isActive: true,
      lastUsedAt: new Date(),
    });

    return this.fcmTokenRepository.save(newToken);
  }
}
