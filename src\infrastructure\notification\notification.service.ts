import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import * as admin from 'firebase-admin';
import * as path from 'path';
import * as fs from 'fs';

@Injectable()
export class NotificationService implements OnModuleInit {
  private readonly logger = new Logger(NotificationService.name);

  onModuleInit() {
    const keyPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH;
    if (!keyPath || !fs.existsSync(keyPath)) {
      throw new Error(
        'Firebase service account key path is invalid or missing.',
      );
    }

    const serviceAccount = require(path.resolve(keyPath));

    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });
      this.logger.log('Firebase Admin initialized');
    }
  }

  async sendPushNotification(
    fcmTokens: string[],
    title: string,
    body: string,
    data: any = {},
  ): Promise<void> {
    if (fcmTokens.length === 0) return;

    console.log({ data });

    for (const token of fcmTokens) {
      const message = {
        token,
        notification: {
          title,
          body,
        },
        data,
      };

      try {
        const response = await admin.messaging().send(message);
        this.logger.log(`Sent notification to ${token}: ${response}`);
      } catch (error) {
        this.logger.error(`Error sending notification to ${token}:`, error);
        // Optional: handle or store invalid tokens for later cleanup
      }
    }
  }
}
