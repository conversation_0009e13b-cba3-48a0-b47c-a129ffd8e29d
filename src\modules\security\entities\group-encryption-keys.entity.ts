import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('group_encryption_keys')
export class GroupEncryptionKey {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ name: 'group_id' })
  groupId: number;

  @Index()
  @Column({ name: 'member_id' })
  memberId: number;

  @Column({ name: 'encrypted_group_key', type: 'text' })
  encryptedGroupKey: string;

  @Column({ name: 'key_version' })
  keyVersion: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne('Group', 'encryptionKeys')
  @JoinColumn({ name: 'group_id' })
  group: any;

  @ManyToOne('OrgMember', 'groupEncryptionKeys')
  @JoinColumn({ name: 'member_id' })
  member: any;
}
