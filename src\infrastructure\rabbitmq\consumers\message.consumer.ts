import { RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { MessagesService } from '../../../modules/messages/services/messages.service';
import { SendMessageDto } from '../../../modules/messages/dto/send-message.dto';
import { NotificationService } from '../../notification/notification.service';
import { SocketGateway } from '../../socket/socket.gateway';
import { StorageService } from '../../../core/storage/storage.service';
import { GroupMembersService } from '../../../modules/groups/services/group-member.service';
import { GroupsService } from '../../../modules/groups/services/groups.service';
import { MembersService } from '../../..//modules/members/services/members.service';

@Injectable()
export class MessageConsumer {
  constructor(
    @Inject(forwardRef(() => GroupMembersService))
    private readonly groupMemberService: GroupMembersService,
    private readonly groupService: GroupsService,
    private readonly socketGateway: SocketGateway,
    private readonly storageService: StorageService,
    private readonly notificationService: NotificationService,
    @Inject(forwardRef(() => MembersService))
    private readonly memberService: MembersService,
    @Inject(forwardRef(() => MessagesService))
    private readonly messageService: MessagesService,
  ) {}

  @RabbitSubscribe({
    exchange: 'message_exchange',
    routingKey: 'message.send',
    queue: 'message_queue',
  })
  async handleIncomingMessage(dto: SendMessageDto) {
    const savedMessage = await this.messageService.saveMessage(dto);

    // Generate signed sender avatar
    if (savedMessage.sender?.imageUrl) {
      const signedUrl = await this.storageService.generateSignedUrl(
        savedMessage.sender.imageUrl,
        86400,
      );
      savedMessage.sender.imageUrl = signedUrl;
    }

    // Emit via Socket.IO
    this.socketGateway.server
      .to(String(savedMessage.groupId))
      .emit('new_message', savedMessage);

    // === Push Notification Logic ===

    // Get all group members (excluding sender)
    if (typeof savedMessage.groupId !== 'number') {
      throw new Error('groupId is required to fetch group members');
    }
    const members = await this.groupMemberService.getGroupMembers(
      savedMessage.groupId,
    );
    const receiverIds = members
      .filter((m) => m.id !== savedMessage.senderId)
      .map((m) => m.id);

    const fcmTokens = await this.memberService.findActiveFcmTokens(receiverIds);

    // ✅ Fetch group info (assumes you have a GroupService or similar)
    if (typeof savedMessage.groupId !== 'number') {
      throw new Error('groupId is required to fetch group info');
    }
    const group = await this.groupService.findOne(savedMessage.groupId);

    // ✅ Generate signed group image URL
    let groupImageUrl = '';
    if (group?.imageUrl) {
      groupImageUrl = await this.storageService.generateSignedUrl(
        group.imageUrl,
        86400,
      );
    }

    // ✅ Prepare string-only data
    const notificationData = {
      messageId: savedMessage.id.toString(),
      groupId: (savedMessage.groupId ?? '').toString(),
      groupName: group?.name || 'Group',
      groupImageUrl: groupImageUrl || '',
      message: savedMessage.content || '',
      // groupKeyVersion: savedMessage.groupKeyVersion.toString(),
      senderName: savedMessage.sender?.name,
    };

    // ✅ Send push
    await this.notificationService.sendPushNotification(
      fcmTokens,
      'New Message',
      `${savedMessage.sender?.name || 'Someone'} sent a message in ${group?.name}`,
      notificationData,
    );
  }
}
