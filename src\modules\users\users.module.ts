import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { UsersService } from './services/users.service';
import { UserController } from './controllers/users.controller';
import { Role } from './entities/role.entity';
import { OrganizationsModule } from '../organization/organizations.module';
import { CoreModule } from '../../core/core.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Role]),
    CoreModule,
    OrganizationsModule,
  ],
  providers: [UsersService],
  controllers: [UserController],
  exports: [UsersService, TypeOrmModule],
})
export class UserModule {}
