import {
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';
import { JwtService } from '@nestjs/jwt';
import {
  REFRESH_TOKEN_EXPIRED,
  INVALID_REFRESH_TOKEN,
  NO_REFRESH_TOKEN_PROVIDED,
} from '../../../common/constants/error.constants';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtRefreshGuard extends AuthGuard('jwt-refresh') {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {
    super();
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractRefreshToken(request);
    if (!token) {
      throw new UnauthorizedException(NO_REFRESH_TOKEN_PROVIDED);
    }

    try {
      const payload = this.jwtService.verify(token, {
        secret: this.configService.get<string>('jwt.refreshSecret'),
      });
      request.user = payload;
    } catch (error) {
      if (error?.name === 'TokenExpiredError') {
        throw new UnauthorizedException({
          message: 'Refresh token has expired',
          code: REFRESH_TOKEN_EXPIRED,
        });
      }

      if (error?.name === 'JsonWebTokenError') {
        throw new UnauthorizedException({
          message: 'Invalid refresh token',
          code: INVALID_REFRESH_TOKEN,
        });
      }

      throw new UnauthorizedException('Refresh token verification failed');
    }

    return (await super.canActivate(context)) as boolean;
  }

  handleRequest(err: any, user: any, info: any) {
    if (err || !user) {
      throw (
        err || new UnauthorizedException(info?.message || INVALID_REFRESH_TOKEN)
      );
    }
    return user;
  }

  private extractRefreshToken(request: Request): string | undefined {
    const cookieToken = request.cookies?.refreshToken;
    const headerToken = request.headers['x-refresh-token'];
    return (
      cookieToken || (typeof headerToken === 'string' ? headerToken : undefined)
    );
  }
}
