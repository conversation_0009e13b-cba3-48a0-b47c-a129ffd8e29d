import {
  MiddlewareConsumer,
  Module,
  NestModule,
  OnApplicationBootstrap,
} from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  databaseConfig,
  jwtConfig,
  redisConfig,
  storageConfig,
  rabbitmqConfig,
  appConfig,
  smsConfig,
} from './config';
import { AuthModule } from './modules/auth/auth.module';
import { GroupsModule } from './modules/groups/groups.module';
import { MembersModule } from './modules/members/members.module';
import { MessagesModule } from './modules/messages/messages.module';
import { OrganizationsModule } from './modules/organization/organizations.module';
import { UserModule } from './modules/users/users.module';
import { MediaModule } from './modules/media/media.module';
import { SecurityModule } from './modules/security/security.module';
import { SubscriptionModule } from './modules/subscription/subscription.module';
import { AuditModule } from './modules/audit/audit.module';
import { ChatModule } from './modules/chat/chat.module';
import { CoreModule } from './core/core.module';
import { InfrastructureModule } from './infrastructure/infrastructure.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductAdminSeeder } from './infrastructure/database/seeders/initial-data.seed';
import { JwtMiddleware, LoggerMiddleware } from './common/middlewares';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { mailConfig } from './config/mail.config';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from './common/guards/jwt-auth.guard';
import { PassportModule } from '@nestjs/passport';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerModule } from '@nestjs/throttler';
import { RabbitMQConfigModule } from './infrastructure/rabbitmq/rabbitMq.module';
import { RedisModule } from './infrastructure/redis/redis.module';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        databaseConfig,
        jwtConfig,
        redisConfig,
        storageConfig,
        rabbitmqConfig,
        appConfig,
        smsConfig,
        mailConfig,
      ],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const databaseConfig = configService.get('database');
        if (!databaseConfig) {
          throw new Error('Database configuration is missing');
        }
        return databaseConfig;
      },
      inject: [ConfigService],
    }),

    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('jwt.secret'),
        signOptions: {
          expiresIn: configService.get<string>('jwt.expiresIn', '1d'),
        },
      }),
    }),

    ScheduleModule.forRoot(),
    RabbitMQConfigModule,
    RedisModule.forRoot(),
    ThrottlerModule.forRoot({
      throttlers: [
        {
          ttl: 60,
          limit: 10,
        },
      ],
    }),

    CoreModule,
    InfrastructureModule,
    UserModule,
    AuthModule,
    GroupsModule,
    MembersModule,
    MessagesModule,
    OrganizationsModule,
    MediaModule,
    SecurityModule,
    SubscriptionModule,
    AuditModule,
    ChatModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    ProductAdminSeeder,
    JwtService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
  ],
})
export class AppModule implements OnApplicationBootstrap, NestModule {
  constructor(private readonly seeder: ProductAdminSeeder) {}

  configure(consumer: MiddlewareConsumer) {
    consumer.apply(JwtMiddleware).forRoutes('*');
    consumer.apply(LoggerMiddleware).forRoutes('*');
  }

  async onApplicationBootstrap() {
    try {
      await this.seeder.seed();
      console.log('Database seeding completed successfully');
    } catch (error) {
      console.error('Error during database initialization:', error);
      throw error;
    }
  }
}
