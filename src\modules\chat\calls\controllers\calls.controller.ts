import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { CallSignalingService } from '../services/call-signaling.service';
import { CallRecordingService } from '../services/call-recording.service';
import { CallQualityService } from '../services/call-quality.service';

/**
 * Calls Controller
 *
 * Handles HTTP requests related to voice and video calls,
 * call management, recording, and quality monitoring.
 */
@ApiTags('calls')
@Controller('calls')
// @UseGuards(JwtAuthGuard)
export class CallsController {
  private readonly logger = new Logger(CallsController.name);

  constructor(
    private readonly callSignalingService: CallSignalingService,
    private readonly callRecordingService: CallRecordingService,
    private readonly callQualityService: CallQualityService,
  ) {}

  /**
   * Initiate a new call
   */
  @Post('initiate')
  @ApiOperation({ summary: 'Initiate a new call' })
  @ApiResponse({ status: 201, description: 'Call initiated successfully' })
  async initiateCall(@Body() callData: any) {
    this.logger.log('Initiating new call');
    const { callerId, calleeId, callType } = callData;
    return this.callSignalingService.initiateCall(callerId, calleeId, callType);
  }

  /**
   * Accept an incoming call
   */
  @Post(':callId/accept')
  @ApiOperation({ summary: 'Accept an incoming call' })
  @ApiResponse({ status: 200, description: 'Call accepted successfully' })
  async acceptCall(@Param('callId') callId: string, @Body() userData: any) {
    this.logger.log(`Accepting call ${callId}`);
    const { userId } = userData;
    return this.callSignalingService.acceptCall(callId, userId);
  }

  /**
   * Reject an incoming call
   */
  @Post(':callId/reject')
  @ApiOperation({ summary: 'Reject an incoming call' })
  @ApiResponse({ status: 200, description: 'Call rejected successfully' })
  async rejectCall(@Param('callId') callId: string, @Body() userData: any) {
    this.logger.log(`Rejecting call ${callId}`);
    const { userId } = userData;
    return this.callSignalingService.rejectCall(callId, userId);
  }

  /**
   * End an active call
   */
  @Post(':callId/end')
  @ApiOperation({ summary: 'End an active call' })
  @ApiResponse({ status: 200, description: 'Call ended successfully' })
  async endCall(@Param('callId') callId: string, @Body() userData: any) {
    this.logger.log(`Ending call ${callId}`);
    const { userId } = userData;
    return this.callSignalingService.endCall(callId, userId);
  }

  /**
   * Get active calls for user
   */
  @Get('active')
  @ApiOperation({ summary: 'Get active calls for user' })
  @ApiResponse({
    status: 200,
    description: 'Active calls retrieved successfully',
  })
  async getActiveCalls(@Query('userId') userId: number) {
    this.logger.log(`Getting active calls for user ${userId}`);
    return this.callSignalingService.getActiveCalls(userId);
  }

  /**
   * Start call recording
   */
  @Post(':callId/recording/start')
  @ApiOperation({ summary: 'Start call recording' })
  @ApiResponse({ status: 201, description: 'Recording started successfully' })
  async startRecording(@Param('callId') callId: string, @Body() userData: any) {
    this.logger.log(`Starting recording for call ${callId}`);
    const { userId } = userData;
    return this.callRecordingService.startRecording(callId, userId);
  }

  /**
   * Stop call recording
   */
  @Post(':callId/recording/stop')
  @ApiOperation({ summary: 'Stop call recording' })
  @ApiResponse({ status: 200, description: 'Recording stopped successfully' })
  async stopRecording(@Param('callId') callId: string, @Body() userData: any) {
    this.logger.log(`Stopping recording for call ${callId}`);
    const { userId } = userData;
    return this.callRecordingService.stopRecording(callId, userId);
  }

  /**
   * Get call quality report
   */
  @Get(':callId/quality')
  @ApiOperation({ summary: 'Get call quality report' })
  @ApiResponse({
    status: 200,
    description: 'Quality report retrieved successfully',
  })
  async getQualityReport(@Param('callId') callId: string) {
    this.logger.log(`Getting quality report for call ${callId}`);
    return this.callQualityService.getQualityReport(callId);
  }
}
