import { Message } from '../../modules/messages/entities/message.entity';

/**
 * Transforms a Message entity into a format suitable for client consumption
 * @param message The message entity to transform
 * @returns Transformed message object
 */
export function tailorMessage(message: any): any {
  if (!message) {
    return null;
  }

  return {
    id: message.id,
    groupId: message.groupId,
    senderId: message.senderId,
    encryptedContent: message.encryptedContent,
    nonce: message.nonce,
    groupKeyVersion: message.groupKeyVersion,
    sentAt: message.sentAt?.toISOString(),
    isDeleted: message.isDeleted || false,
    replyToMessageId: message.replyToMessageId,
    
    // Sender information
    sender: message.sender ? {
      id: message.sender.id,
      name: message.sender.name,
      imageUrl: message.sender.imageUrl,
    } : null,
    
    // File information
    file: message.file ? {
      id: message.file.id,
      fileUrl: message.file.fileUrl,
      fileName: message.file.filename,
      fileType: message.file.fileType,
      fileSize: message.file.fileSize,
      metaData: message.file.metaData,
    } : null,
    
    // Reply information
    replyTo: message.replyTo ? {
      id: message.replyTo.id,
      encryptedContent: message.replyTo.encryptedContent,
      nonce: message.replyTo.nonce,
      groupKeyVersion: message.replyTo.groupKeyVersion,
      sentAt: message.replyTo.sentAt?.toISOString(),
      sender: message.replyTo.sender ? {
        id: message.replyTo.sender.id,
        name: message.replyTo.sender.name,
        imageUrl: message.replyTo.sender.imageUrl,
      } : null,
    } : null,
    
    // Read receipts
    readBy: message.reads ? message.reads.map((read: any) => ({
      id: read.readerId,
      readAt: read.readAt?.toISOString(),
    })) : [],
    
    // Metadata
    encryptedMetaData: message.encryptedMetaData || {},
    createdAt: message.createdAt?.toISOString(),
    updatedAt: message.updatedAt?.toISOString(),
  };
}
