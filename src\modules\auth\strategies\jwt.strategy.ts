import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Request } from 'express';
import { User } from '../../users/entities/user.entity';
import { OrgMember } from '../../members/entities/org-member.entity';
import { USER_NOT_FOUND } from '../../../common/constants/error.constants';
import { UserType } from '../../../common/types/user-type';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    private configService: ConfigService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(OrgMember)
    private orgMemberRepository: Repository<OrgMember>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        JwtStrategy.extractTokenFromHeaderOrCookie,
      ]),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('jwt.secret'),
    });
  }

  private static extractTokenFromHeaderOrCookie(req: Request): string | null {
    // Try Authorization header
    const authHeader = req.headers.authorization;
    if (authHeader?.startsWith('Bearer ')) {
      return authHeader.split(' ')[1];
    }

    // Fallback: Try cookie
    if (req.cookies?.access_token) {
      return req.cookies.access_token;
    }

    return null;
  }

  async validate(payload: {
    sub: number;
    email: string;
    roleId?: number;
    orgId?: number;
    type: UserType;
  }): Promise<User | OrgMember> {
    if (payload.type === 'product_admin') {
      const user = await this.userRepository.findOne({
        where: { id: payload.sub },
        relations: ['role'],
      });

      if (!user) throw new UnauthorizedException(USER_NOT_FOUND);
      return user;
    } else if (payload.type === 'org_admin') {
      const user = await this.userRepository.findOne({
        where: { id: payload.sub },
        relations: ['role', 'organization'],
      });

      if (!user) throw new UnauthorizedException(USER_NOT_FOUND);
      return user;
    } else if (payload.type === 'org_member') {
      const member = await this.orgMemberRepository.findOne({
        where: { id: payload.sub },
        relations: ['organization'],
      });

      if (!member) throw new UnauthorizedException(USER_NOT_FOUND);
      return member;
    }

    throw new UnauthorizedException('Invalid user type in token.');
  }
}
