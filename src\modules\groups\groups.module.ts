import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Group } from './entities/group.entity';
import { GroupMember } from './entities/group-member.entity';
import { GroupsService } from './services/groups.service';
import { GroupsController } from './controllers/groups.controller';
import { EncryptionModule } from '../../core/encryption/encryption.module';
import { User } from '../users/entities/user.entity';
import { Organization } from '../organization/entities/organization.entity';
import { MailModule } from '../../core/mail/mail.module';
import { StorageModule } from '../../core/storage/storage.module';
import { GroupEncryptionService } from '../security/services/group-encryption.service';
import { GroupEncryptionKey } from '../security/entities/group-encryption-keys.entity';
import { GroupMembersService } from './services/group-member.service';
import { GroupMembersController } from './controllers/group-member.controller';
import { MessagesModule } from '../messages/messages.module';
import { MembersModule } from '../members/members.module';
import { SocketModule } from '../../infrastructure/socket/socket.module';
import { GroupMetaLog } from '../audit/entities/group-meta-logs.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Group,
      GroupMember,
      User,
      Organization,
      GroupEncryptionKey,
      GroupMetaLog,
    ]),
    EncryptionModule,
    forwardRef(() => MembersModule),
    MailModule,
    StorageModule,
    forwardRef(() => MessagesModule),
    forwardRef(() => SocketModule),
  ],
  providers: [GroupsService, GroupEncryptionService, GroupMembersService],
  controllers: [GroupsController, GroupMembersController],
  exports: [
    GroupsService,
    GroupMembersService,
    GroupEncryptionService,
    TypeOrmModule,
  ],
})
export class GroupsModule {}
