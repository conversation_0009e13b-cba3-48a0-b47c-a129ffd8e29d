import {
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsBoolean,
} from 'class-validator';
import { CreateOrganizationDto } from '../../organization/dto';

export class CreateUserDto {
  @IsOptional()
  @IsString()
  username?: string;

  @IsOptional()
  @IsString()
  password?: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsNumber()
  roleId: number = 2;

  @IsOptional()
  @IsString()
  fileUrl?: string;

  @IsOptional()
  organization?: CreateOrganizationDto;
}
