import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ChainKeyState } from '../../../security/entities/chain-key-states.entity';

@Entity('message_keys')
@Index(['chainStateId', 'messageNumber'])
export class MessageKey {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  chainStateId: number;

  @Column()
  messageNumber: number;

  @Column({ type: 'text' })
  messageKey: string;

  @Column({ default: false })
  used: boolean;

  @CreateDateColumn()
  createdAt: Date;

  // Relations
  @ManyToOne(() => ChainKeyState, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'chainStateId' })
  chainState: ChainKeyState;
}
