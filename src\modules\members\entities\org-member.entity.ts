import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  CreateDateColumn,
} from 'typeorm';
import { MemberFcmToken } from './member-fcm-token.entity';
import { GroupEncryptionKey } from '../../security/entities/group-encryption-keys.entity';
import { Expose, Transform } from 'class-transformer';

@Entity('org_members')
export class OrgMember {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'org_id' })
  orgId: number;

  @Column()
  name: string;

  @Column({ name: 'phone_no', unique: true })
  phoneNo: string;

  @Column({ unique: true })
  email: string;

  @Column({ name: 'image_url', nullable: true })
  imageUrl: string;

  @Column({ name: 'is_verified', default: false })
  isVerified: boolean;

  @Column({ name: 'last_login_at', nullable: true })
  lastLoginAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  @Expose()
  @Transform(({ value }) => {
    if (value instanceof Date) {
      return value.toISOString().split('T')[0];
    }
    return value;
  })
  createdAt: Date;

  @Column('text', { nullable: true })
  publicKey: string;

  @Column('text', { nullable: true })
  encryptedPrivateKey: string;

  @Column({ nullable: true })
  keyCreatedAt: Date;

  @ManyToOne('Organization', 'members')
  @JoinColumn({ name: 'org_id' })
  organization: any;

  @OneToMany('GroupMember', 'member')
  groupMemberships: any[];

  @OneToMany('OtpVerification', 'member')
  otpVerifications: any[];

  @OneToMany('Message', 'sender')
  sentMessages: any[];

  @OneToMany('GroupMessageRead', 'reader')
  readMessages: any[];

  @OneToMany('MessageVisibility', 'member')
  messageVisibilities: any[];

  @OneToMany(() => MemberFcmToken, (fcmToken) => fcmToken.member)
  fcmTokens: MemberFcmToken[];

  @OneToMany(() => GroupEncryptionKey, (encryptionKey) => encryptionKey.member)
  groupEncryptionKeys: GroupEncryptionKey[];
}
