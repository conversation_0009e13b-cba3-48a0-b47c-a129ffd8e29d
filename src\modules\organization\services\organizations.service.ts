import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Organization } from '../entities/organization.entity';
import { CreateOrganizationDto, UpdateOrganizationDto } from '../dto';

@Injectable()
export class OrganizationsService {
  constructor(
    @InjectRepository(Organization)
    private organizationRepository: Repository<Organization>,
  ) {}

  async create(
    createOrganizationDto: CreateOrganizationDto,
  ): Promise<Organization> {
    // Check if organization with name already exists
    const existingOrg = await this.organizationRepository.findOne({
      where: { name: createOrganizationDto.name },
    });

    if (existingOrg) {
      throw new ConflictException('Organization with this name already exists');
    }

    const organization = this.organizationRepository.create(
      createOrganizationDto,
    );
    return this.organizationRepository.save(organization);
  }

  async findAll() {
    const organizations = await this.organizationRepository.find({
      relations: ['admins'],
    });

    return organizations.map((org) => {
      const admin = org.admins?.[0]; // Assuming only one admin per organization
      return {
        id: org.id,
        name: org.name,
        location: org.location,
        phoneNo: org.phoneNo,
        status: org.status,
        administratorEmail: admin?.email || null,
        imageUrl: org.imageUrl,
      };
    });
  }

  async findOne(id: number): Promise<Organization> {
    const organization = await this.organizationRepository.findOne({
      where: { id },
      relations: ['admins', 'members', 'groups'],
    });

    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }

    // Remove password field from admins in the returned object, but do not mutate the entity
    const sanitizedOrganization = {
      ...organization,
      admins: organization.admins?.map(({ password, ...rest }) => rest) || [],
    };

    return sanitizedOrganization as Organization;
  }

  async findByName(name: string): Promise<Organization | null> {
    return this.organizationRepository.findOne({ where: { name } });
  }

  async update(
    id: number,
    updateOrganizationDto: UpdateOrganizationDto,
  ): Promise<Organization> {
    // Check if organization exists
    const organization = await this.findOne(id);

    // Check if name is being updated and if it's already in use
    if (
      updateOrganizationDto.name &&
      updateOrganizationDto.name !== organization.name
    ) {
      const existingOrg = await this.organizationRepository.findOne({
        where: { name: updateOrganizationDto.name },
      });

      if (existingOrg) {
        throw new ConflictException(
          'Organization with this name already exists',
        );
      }
    }

    // Update and save
    await this.organizationRepository.update(id, updateOrganizationDto);
    return this.findOne(id);
  }

  async remove(id: number): Promise<void> {
    const organization = await this.findOne(id);
    await this.organizationRepository.remove(organization);
  }
}
