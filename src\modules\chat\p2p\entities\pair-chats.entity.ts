import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { OrgMember } from '../../../members/entities/org-member.entity';

@Entity('pair_chats')
@Index(['member1Id', 'member2Id'], { unique: true })
@Index(['orgId'])
export class PairChat {
  @PrimaryGeneratedColumn()
  id: number;

  // Always store smaller ID as member1Id for consistency
  @Column()
  member1Id: number;

  @Column()
  member2Id: number;

  @Column()
  orgId: number;

  @Column({ default: 1 })
  currentKeyVersion: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true })
  lastMessageAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => OrgMember)
  @JoinColumn({ name: 'member1Id' })
  member1: OrgMember;

  @ManyToOne(() => OrgMember)
  @JoinColumn({ name: 'member2Id' })
  member2: OrgMember;
}
