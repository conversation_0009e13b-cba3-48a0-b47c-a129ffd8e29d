import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import {
  TOKEN_EXPIRED,
  INVALID_TOKEN,
  NO_TOKEN_PROVIDED,
} from '../../../common/constants/error.constants';
import { WsException } from '@nestjs/websockets';

@Injectable()
export class WsAuthInterceptor implements NestInterceptor {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const client = context.switchToWs().getClient();
    const token =
      client.handshake?.auth?.token ||
      client.handshake?.headers?.authorization?.split(' ')[1];

    if (!token) {
      throw new WsException(NO_TOKEN_PROVIDED);
    }

    try {
      const payload = this.jwtService.verify(token, {
        secret: this.configService.get<string>('jwt.secret'),
      });

      client.data.memberId = client.handshake?.auth?.memberId;
      client.data.deviceId = client.handshake.auth?.deviceId;
    } catch (error) {
      if (error?.name === 'TokenExpiredError') {
        throw new WsException({
          message: 'Token has expired',
          code: TOKEN_EXPIRED,
        });
      }

      if (error?.name === 'JsonWebTokenError') {
        throw new WsException({
          message: 'Invalid token',
          code: INVALID_TOKEN,
        });
      }

      throw new WsException('Authentication failed');
    }

    return next.handle();
  }
}
