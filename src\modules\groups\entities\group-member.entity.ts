import { Expose, Transform } from 'class-transformer';
import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('group_members')
export class GroupMember {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'group_id' })
  groupId: number;

  @Column({ name: 'member_id' })
  memberId: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'joined_at' })
  @Expose()
  @Transform(({ value }) => {
    if (value instanceof Date) {
      return value.toISOString().split('T')[0];
    }
    return value;
  })
  joinedAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  @Expose()
  @Transform(({ value }) => {
    if (value instanceof Date) {
      return value.toISOString().split('T')[0];
    }
    return value;
  })
  updatedAt: Date;

  @Column({ name: 'left_at', nullable: true })
  leftAt: Date;

  @Column({ name: 'is_mute', default: false })
  isMute: boolean;

  @ManyToOne('Group', 'members')
  @JoinColumn({ name: 'group_id' })
  group: any;

  @ManyToOne('OrgMember', 'groupMemberships')
  @JoinColumn({ name: 'member_id' })
  member: any;
}
