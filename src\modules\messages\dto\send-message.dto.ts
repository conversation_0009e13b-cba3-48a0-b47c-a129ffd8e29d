import {
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsNotEmpty,
  IsObject,
} from 'class-validator';

export class SendMessageDto {
  @IsNumber()
  groupId: number;

  @IsNumber()
  senderId: number;

  @IsString()
  @IsNotEmpty()
  encryptedContent: string;

  @IsString()
  @IsNotEmpty()
  nonce: string;

  @IsObject()
  @IsOptional()
  encryptedMetaData?: Record<string, any>;

  @IsNumber()
  @IsOptional()
  replyToMessageId?: number;

  @IsNumber()
  @IsOptional()
  fileId?: number;

  @IsOptional()
  sentAt?: Date;
}
