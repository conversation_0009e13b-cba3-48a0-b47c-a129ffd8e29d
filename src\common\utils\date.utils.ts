export function getISTTimeString(): string {
  // Create a date object for the current time
  const now = new Date();

  // Convert to IST by creating a date string with the IST timezone
  const istDate = new Date(
    now.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }),
  );

  // Format to ISO string - this will be in UTC format
  // Then modify it to ensure it ends with the correct IST offset (+05:30)
  return istDate.toISOString().replace(/Z$/, '+05:30');
}

// If you need just the date part in ISO format (YYYY-MM-DD), use this function:
export function getISTDateString(): string {
  const now = new Date();
  const istDate = new Date(
    now.toLocaleString('en-US', { timeZone: 'Asia/Kolkata' }),
  );

  return istDate.toISOString().split('T')[0];
}
