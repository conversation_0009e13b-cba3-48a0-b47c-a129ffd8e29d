import {
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, In, MoreThan, Not, Repository } from 'typeorm';
import { SendMessageDto } from '../dto/send-message.dto';
import { Message } from '../entities/message.entity';
import { MessageVisibility } from '../entities/message-visibility.entity';
import { MembersService } from '../../members/services/members.service';
import { GroupsService } from '../../groups/services/groups.service';
import { GroupMembersService } from '../../groups/services/group-member.service';
import { Group } from '../../groups/entities/group.entity';

@Injectable()
export class MessagesService {
  constructor(
    @InjectRepository(Message)
    private readonly messageRepo: Repository<Message>,

    @InjectRepository(MessageVisibility)
    private readonly messageVisibilityRepository: Repository<MessageVisibility>,

    private readonly membersService: MembersService,

    @Inject(forwardRef(() => GroupsService))
    private readonly groupsService: GroupsService,

    @Inject(forwardRef(() => GroupMembersService))
    private readonly groupmembersService: GroupMembersService,
  ) {}

  async saveMessage(dto: SendMessageDto): Promise<Message> {
    const group = await this.groupsService.findOne(Number(dto.groupId));
    const sender = await this.membersService.findOne(dto.senderId);
    if (!group || !sender) {
      throw new Error('Invalid sender or group');
    }

    const file = dto.fileId
      ? await this.fileRepo.findOneBy({ id: dto.fileId })
      : null;

    const messageToSave = this.messageRepo.create({
      group: group,
      sender: sender,
      groupKeyVersion: group.currentKeyVersion,
      encryptedContent: dto.encryptedContent,
      encryptedMetaData: dto.encryptedMetaData || {},
      nonce: dto.nonce,
      replyToMessageId:
        dto.replyToMessageId !== undefined ? dto.replyToMessageId : undefined,
      file: file || undefined,
      sentAt: dto.sentAt || new Date(),
    });

    const savedMessage = await this.messageRepo.save(messageToSave);

    const message = await this.messageRepo.findOne({
      where: { id: savedMessage.id },
      relations: [
        'sender',
        'file',
        'visibilities',
        'replyTo',
        'replyTo.sender',
        'reads',
      ],
    });
    if (!message) {
      throw new NotFoundException('Message not found after saving');
    }
    return message;
  }

  async markMessageAsRead(
    groupId: number,
    readerId: number,
  ): Promise<GroupMessageRead[] | null> {
    const existingMessages = await this.messageRepo.find({
      where: { groupId, isDeleted: false },
      select: ['id'],
    });
    const allMessageIds = existingMessages.map((m) => m.id);

    if (allMessageIds.length === 0) {
      console.log('No messages found to mark as read');
      return null;
    }
    const alreadyRead = await this.groupMessageReadRepo.find({
      where: {
        readerId,
        messageId: In(allMessageIds),
      },
      select: ['messageId'],
    });
    const alreadyReadIds = new Set(alreadyRead.map((r) => r.messageId));
    const unreadMessageIds = allMessageIds.filter(
      (id) => !alreadyReadIds.has(id),
    );

    if (unreadMessageIds.length === 0) {
      console.log('All messages already read');
      return null;
    }

    const newReads = unreadMessageIds.map((messageId) =>
      this.groupMessageReadRepo.create({
        messageId,
        readerId,
        readAt: new Date(),
      }),
    );

    const savedReads = await this.groupMessageReadRepo.save(newReads);

    return savedReads.length > 0 ? savedReads : null;
  }

  async findMessagesByGroup(groupId: number): Promise<Message[]> {
    return this.messageRepo.find({
      where: { group: { id: groupId } },
      order: { sentAt: 'DESC' },
      relations: ['sender', 'file', 'replyTo'],
    });
  }

  async countGroupMessagesReadByMember(
    groupId: number,
    memberId: number,
  ): Promise<number> {
    return await this.groupMessageReadRepo.count({
      where: {
        message: { groupId },
        readerId: memberId,
      },
    });
  }
  async findOneMessage(id: number): Promise<Message | null> {
    return this.messageRepo.findOne({ where: { id } });
  }

  async getTotalMessagesInGroup(
    groupId: number,
    joinedAt?: Date,
  ): Promise<number> {
    const whereCondition: any = {
      groupId,
      isDeleted: false,
    };

    if (joinedAt) {
      whereCondition.sentAt = MoreThan(joinedAt);
    }

    return this.messageRepo.count({
      where: whereCondition,
    });
  }

  async getLastMessageInGroup(
    groupId: number,
    joinedAt?: Date,
    lastSyncDate?: Date,
  ): Promise<Message | null> {
    const whereCondition: any = {
      groupId,
      isDeleted: false,
      ...(lastSyncDate && { updatedAt: MoreThan(lastSyncDate) }),
    };

    if (joinedAt) {
      whereCondition.sentAt = MoreThan(joinedAt);
    }

    return this.messageRepo.findOne({
      where: whereCondition,
      order: { sentAt: 'DESC' },
      relations: ['sender'],
    });
  }

  async deleteMessageForMember(
    messageId: number,
    memberId: number,
  ): Promise<void> {
    console.log(`Deleting message ${messageId} for member ${memberId}`);

    const repo = this.messageVisibilityRepository;

    const existing = await repo.findOne({
      where: { messageId, memberId },
    });

    if (existing) {
      // Soft delete
      await repo.softRemove(existing);
    } else {
      // Create and soft delete if not already tracked
      const newVisibility = repo.create({
        messageId,
        memberId,
        isVisible: false,
      });
      const savedVisibility = await repo.save(newVisibility);
    }
  }

  async getMessagesInGroup(
    groupId: number,
    joinedAt?: Date,
  ): Promise<Message[]> {
    const whereCondition: any = {
      groupId,
      isDeleted: false,
    };

    if (joinedAt) {
      whereCondition.sentAt = MoreThan(joinedAt);
    }

    const messages = await this.messageRepo.find({
      where: whereCondition,
      relations: [
        'sender',
        'file',
        'visibilities',
        'replyTo',
        'replyTo.sender',
        'reads',
      ],
      order: { sentAt: 'DESC' },
    });

    return messages;
  }

  async deleteMessagesForEveryone(
    messageIds: number[],
    memberId: number,
  ): Promise<number[]> {
    const messages = await this.messageRepo.findBy({
      id: In(messageIds),
      isDeleted: false,
    });

    const notFound = messageIds.filter(
      (id) => !messages.find((msg) => msg.id === id),
    );
    if (notFound.length) {
      throw new NotFoundException(`Messages not found: ${notFound.join(', ')}`);
    }

    const unauthorized = messages.filter((msg) => msg.senderId !== memberId);
    if (unauthorized.length) {
      throw new ForbiddenException(
        `Only your own messages can be deleted. Unauthorized: ${unauthorized
          .map((m) => m.id)
          .join(', ')}`,
      );
    }

    for (const msg of messages) {
      msg.isDeleted = true;
      msg.deletedBy = memberId;
      msg.deletedAt = new Date();
    }

    await this.messageRepo.save(messages);

    return messages.map((msg) => msg.id); // return deleted IDs
  }

  async hideMessageForMember(
    messageId: number,
    memberId: number,
  ): Promise<void> {
    const existing = await this.messageVisibilityRepository.findOne({
      where: { messageId, memberId },
    });

    if (existing) {
      existing.isVisible = false;
      existing.deletedAt = new Date();
      await this.messageVisibilityRepository.save(existing);
    } else {
      await this.messageVisibilityRepository.save({
        messageId,
        memberId,
        isVisible: false,
        deletedAt: new Date(),
      });
    }
  }

  async getDeletedMessagesSince(groupId: number, lastOffline: Date) {
    return this.messageRepo.find({
      where: {
        groupId,
        isDeleted: true,
        deletedAt: MoreThan(lastOffline),
      },
      select: ['id'],
    });
  }

  async getMessagesInGroupBetween(
    groupId: number,
    fromRaw: Date | string,
    toRaw: Date | string,
  ) {
    const from = new Date(fromRaw);
    const to = new Date(toRaw);

    const fromUTC = new Date(from.toISOString());
    const toUTC = new Date(to.toISOString());
    return this.messageRepo.find({
      where: {
        groupId,
        sentAt: Between(fromUTC, toUTC),
      },
      relations: ['sender', 'file', 'visibilities', 'reads'],
      order: { sentAt: 'DESC' },
    });
  }
}
