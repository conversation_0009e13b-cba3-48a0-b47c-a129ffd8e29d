import {
  Controller,
  Get,
  Post,
  Query,
  Body,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ComplianceReportingService } from '../services/compliance-reporting.service';

/**
 * Compliance Controller
 *
 * Handles HTTP requests related to compliance reporting,
 * regulatory requirements, and data protection compliance.
 */
@ApiTags('compliance')
@Controller('compliance')
// @UseGuards(JwtAuthGuard, RolesGuard)
export class ComplianceController {
  private readonly logger = new Logger(ComplianceController.name);

  constructor(
    private readonly complianceReportingService: ComplianceReportingService,
  ) {}

  /**
   * Generate GDPR compliance report
   */
  @Post('reports/gdpr')
  @ApiOperation({ summary: 'Generate GDPR compliance report' })
  @ApiResponse({
    status: 201,
    description: 'GDPR report generated successfully',
  })
  async generateGDPRReport(@Body() reportParams: any) {
    this.logger.log('Generating GDPR compliance report');
    const { startDate, endDate } = reportParams;
    return this.complianceReportingService.generateGDPRReport(
      new Date(startDate),
      new Date(endDate),
    );
  }

  /**
   * Generate data processing report
   */
  @Post('reports/data-processing')
  @ApiOperation({ summary: 'Generate data processing report' })
  @ApiResponse({
    status: 201,
    description: 'Data processing report generated successfully',
  })
  async generateDataProcessingReport(@Body() reportParams: any) {
    this.logger.log('Generating data processing report');
    const { organizationId, startDate, endDate } = reportParams;
    return this.complianceReportingService.generateDataProcessingReport(
      organizationId,
      new Date(startDate),
      new Date(endDate),
    );
  }

  /**
   * Generate consent report
   */
  @Post('reports/consent')
  @ApiOperation({ summary: 'Generate user consent report' })
  @ApiResponse({
    status: 201,
    description: 'Consent report generated successfully',
  })
  async generateConsentReport(@Body() reportParams: any) {
    this.logger.log('Generating consent report');
    const { organizationId, startDate, endDate } = reportParams;
    return this.complianceReportingService.generateConsentReport(
      organizationId,
      new Date(startDate),
      new Date(endDate),
    );
  }

  /**
   * Generate data breach report
   */
  @Post('reports/breach')
  @ApiOperation({ summary: 'Generate data breach report' })
  @ApiResponse({
    status: 201,
    description: 'Breach report generated successfully',
  })
  async generateBreachReport(@Body() reportParams: any) {
    this.logger.log('Generating breach report');
    const { startDate, endDate } = reportParams;
    return this.complianceReportingService.generateBreachReport(
      new Date(startDate),
      new Date(endDate),
    );
  }

  /**
   * Export compliance data
   */
  @Post('export')
  @ApiOperation({ summary: 'Export compliance data' })
  @ApiResponse({
    status: 201,
    description: 'Compliance data exported successfully',
  })
  async exportComplianceData(@Body() exportParams: any) {
    this.logger.log('Exporting compliance data');
    const { organizationId, format } = exportParams;
    return this.complianceReportingService.exportComplianceData(
      organizationId,
      format,
    );
  }
}
