import { IsString, IsNotEmpty, Length, IsEnum } from 'class-validator';
import { DeviceType } from '../../members/entities/member-fcm-token.entity';

export class VerifyOtpDto {
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: 'OTP must be 6 digits' })
  otpCode: string;

  @IsString()
  @IsNotEmpty({ message: 'Device ID is required' })
  deviceId: string;

  @IsEnum(DeviceType, { message: 'Device type must be either android or ios' })
  @IsNotEmpty()
  deviceType: DeviceType;

  @IsString()
  @IsNotEmpty()
  fcmToken: string;
}
