import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubscriptionService } from './services/subscription.service';
import { BillingService } from './services/billing.service';
import { UsageService } from './services/usage.service';
import { PlanService } from './services/plan.service';
import { SubscriptionController } from './controllers/subscription.controller';
import { BillingController } from './controllers/billing.controller';
import { PlanController } from './controllers/plan.controller';
import { UsageController } from './controllers/usage.controller';

/**
 * Subscription Module
 *
 * Handles subscription management, billing, feature access control,
 * usage tracking, quota monitoring, and plan management for
 * the chat application's subscription-based features.
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Add subscription-related entities here when created
    ]),
  ],
  controllers: [
    SubscriptionController,
    BillingController,
    PlanController,
    UsageController,
  ],
  providers: [SubscriptionService, BillingService, UsageService, PlanService],
  exports: [SubscriptionService, BillingService, UsageService, PlanService],
})
export class SubscriptionModule {}
