import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

@Injectable()
export class SmsService {
  constructor(private readonly configService: ConfigService) {}

  /** Send SMS Verification */
  async sendSmsVerification(mobileNumber: string, otp: string) {
    const message = `Your OTP: ${otp}. Use this to verify your account.`;

    try {
      await axios.post(
        this.configService.get<string>('TEXT_LK_API_URL') || '',
        {
          recipient: mobileNumber,
          sender_id: this.configService.get<string>('TEXT_LK_SENDER_ID'),
          type: 'plain',
          message,
        },
        {
          headers: {
            Authorization: `Bearer ${this.configService.get<string>('TEXT_LK_API_KEY')}`,
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
        },
      );
    } catch (error) {
      console.error('SMS sending failed:', error);
      throw error;
    }
  }
}
