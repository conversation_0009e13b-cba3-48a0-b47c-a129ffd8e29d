import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  Unique,
} from 'typeorm';
import { OrgMember } from './org-member.entity';

export enum DeviceType {
  ANDROID = 'android',
  IOS = 'ios',
  WEB = 'web',
}

@Entity('member_fcm_tokens')
@Unique(['memberId', 'deviceId'])
export class MemberFcmToken {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ name: 'member_id' })
  memberId: number;

  @Column({ name: 'fcm_token', length: 255 })
  fcmToken: string;

  @Column({ name: 'device_id', nullable: true })
  deviceId: string;

  @Column({
    name: 'device_type',
    type: 'enum',
    enum: DeviceType,
    nullable: true,
  })
  deviceType: DeviceType;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'last_used_at', type: 'timestamp', nullable: true })
  lastUsedAt: Date;

  @Column({ name: 'deleted_at', type: 'timestamp', nullable: true })
  deletedAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => OrgMember, (orgMember) => orgMember.fcmTokens, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'member_id' })
  member: OrgMember;
}
