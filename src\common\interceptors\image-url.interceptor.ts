import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Lo<PERSON>,
} from '@nestjs/common';
import { Observable, from } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { StorageService } from '../../core/storage/storage.service';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { PERMANENT_URLS } from '../constants/interceptor.constants';

/**
 * Interceptor to transform image and file paths into signed URLs
 */
@Injectable()
export class ImageUrlInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ImageUrlInterceptor.name);
  private readonly imageUrlExpiry: number;
  private readonly permanentExpiry: number;

  constructor(
    private readonly storageService: StorageService,
    private readonly configService: ConfigService,
    private readonly reflector: Reflector,
  ) {
    this.imageUrlExpiry = this.configService.get<number>(
      'storage.imageUrlExpiry',
      86400,
    );

    // Very long expiration time for "permanent" URLs (10 years in seconds)
    this.permanentExpiry = this.configService.get<number>(
      'storage.permanentUrlExpiry',
      315360000,
    );
  }

  /**
   * Recursively processes objects to replace image and file paths with signed URLs
   */
  private async processObject(
    obj: any,
    permanentFields: string[] = [],
    path: string = '',
  ): Promise<any> {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return Promise.all(
        obj.map((item, index) =>
          this.processObject(
            item,
            permanentFields,
            path ? `${path}[${index}]` : `[${index}]`,
          ),
        ),
      );
    }

    const result = { ...obj };
    const promises: Promise<any>[] = [];

    for (const key in result) {
      if (Object.prototype.hasOwnProperty.call(result, key)) {
        const currentPath = path ? `${path}.${key}` : key;

        if (typeof result[key] === 'object' && result[key] !== null) {
          promises.push(
            this.processObject(result[key], permanentFields, currentPath).then(
              (processed) => {
                result[key] = processed;
              },
            ),
          );
        } else if (
          // Check for fileUrl and imageUrl properties that contain relative paths
          (key === 'fileUrl' ||
            key === 'imageUrl' ||
            key.endsWith('FileUrl') ||
            key.endsWith('ImageUrl')) &&
          typeof result[key] === 'string' &&
          result[key].length > 0 &&
          !result[key].startsWith('http')
        ) {
          // Check if this field should have a permanent URL
          const isPermanent = permanentFields.some((field) => {
            // Check exact match or wildcard patterns like "user.*" or "*.profileImage"
            if (field === '*' || field === currentPath) {
              return true;
            }

            // Handle wildcard at the beginning (e.g., "*.imageUrl")
            if (
              field.startsWith('*') &&
              currentPath.endsWith(field.substring(1))
            ) {
              return true;
            }

            // Handle wildcard at the end (e.g., "user.*")
            if (
              field.endsWith('*') &&
              currentPath.startsWith(field.substring(0, field.length - 1))
            ) {
              return true;
            }

            return false;
          });

          const expiryTime = isPermanent
            ? this.permanentExpiry
            : this.imageUrlExpiry;

          promises.push(
            this.storageService
              .generateSignedUrl(result[key], expiryTime)
              .then((url) => {
                result[key] = url;
              })
              .catch((error) => {
                this.logger.error(
                  `Failed to generate signed URL for ${result[key]}: ${error.message}`,
                  error.stack,
                );
              }),
          );
        }
      }
    }

    await Promise.all(promises);
    return result;
  }

  /**
   * Intercepts the response and transforms image paths to URLs
   */
  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    // Get permanent URL fields from controller metadata
    const permanentUrlFields =
      this.reflector.get<string[]>(PERMANENT_URLS, context.getHandler()) || [];

    return next.handle().pipe(
      switchMap((data) => {
        if (!data) {
          return from([data]);
        }

        return from(
          this.processObject(data, permanentUrlFields).catch((error) => {
            this.logger.error(
              `Error processing image URLs: ${error.message}`,
              error.stack,
            );
            return data; // Fall back to original data
          }),
        );
      }),
    );
  }
}
