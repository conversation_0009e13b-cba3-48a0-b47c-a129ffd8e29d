import { forwardRef, Module } from '@nestjs/common';
import { SocketGateway } from './socket.gateway';
import { WsAuthInterceptor } from './interceptors/ws-auth.interceptor';
import { RedisService } from '../redis/redis.service';
import { JwtService } from '@nestjs/jwt';
import { UserPresenceHandler } from './handlers/user-presence.handler';
import { SystemHandler } from './handlers/system.handler';
import { MessageHandler } from './handlers/message.handler';
import { QueueModule } from '../queue/queue.module';
import { GroupsModule } from '../../modules/groups/groups.module';
import { MessagesModule } from '../../modules/messages/messages.module';
import { MembersModule } from '../../modules/members/members.module';
import { RabbitMQConfigModule } from '../rabbitmq/rabbitMq.module';

@Module({
  imports: [
    forwardRef(() => QueueModule),
    forwardRef(() => GroupsModule),
    forwardRef(() => MessagesModule),
    forwardRef(() => MembersModule),
    RabbitMQConfigModule,
  ],
  providers: [
    SocketGateway,
    WsAuthInterceptor,
    RedisService,
    JwtService,
    UserPresenceHandler,
    SystemHandler,
    MessageHandler,
  ],
  exports: [
    SocketGateway,
    WsAuthInterceptor,
    UserPresenceHandler,
    SystemHandler,
    MessageHandler,
  ],
})
export class SocketModule {}
