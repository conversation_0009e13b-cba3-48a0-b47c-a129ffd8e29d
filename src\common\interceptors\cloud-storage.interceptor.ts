import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  BadRequestException,
  InternalServerErrorException,
  HttpException,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { StorageService } from '../../core/storage/storage.service';
import { Request } from 'express';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CloudStorageInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CloudStorageInterceptor.name);
  private readonly allowedExtensions = [
    'pdf',
    'docx',
    'pptx',
    'png',
    'jpg',
    'jpeg',
  ];
  private readonly folderName: string;

  constructor(
    private readonly storageService: StorageService,
    private readonly configService: ConfigService,
  ) {
    this.folderName = this.configService.get<string>(
      'storage.defaultFolder',
      'uploads',
    );
  }

  async intercept(
    context: ExecutionContext,
    next: <PERSON><PERSON><PERSON><PERSON>,
  ): Promise<Observable<any>> {
    const request = context.switchToHttp().getRequest<Request>();
    const customFolder = request.headers['x-storage-folder'] as string;
    const folder = customFolder || this.folderName;

    const file = request.file;
    const files = request.files as Express.Multer.File[];

    if (!file && (!files || files.length === 0)) {
      return next.handle();
    }

    try {
      if (file) {
        this.validateFile(file);
        const filePath = await this.storageService.uploadFile(file, folder);
        request.body.fileUrl = filePath;
        this.logger.log(`Single file stored at: ${filePath}`);
      }

      if (files && files.length > 0) {
        files.forEach(this.validateFile.bind(this));
        const filePaths = await this.storageService.uploadFiles(files, folder);
        request.body.fileUrls = filePaths;
        this.logger.log(`Multiple files stored: ${filePaths.length} files`);
      }

      return next.handle().pipe(
        map((data) => {
          if (data && typeof data === 'object') {
            if (file && request.body.fileUrl) {
              data.fileUrl = request.body.fileUrl;
            }

            if (files && request.body.fileUrls) {
              data.fileUrls = request.body.fileUrls;
            }
          }
          return data;
        }),
        catchError((error) => {
          this.logger.error('Error in storage interceptor', error.stack);
          return throwError(() => error); // propagate the original error
        }),
      );
    } catch (error) {
      this.logger.error(`File upload error: ${error.message}`, error.stack);

      // Cleanup any uploaded files
      if (request.body.fileUrl) {
        await this.storageService
          .deleteFileByPath(request.body.fileUrl)
          .catch((err) => {
            this.logger.error(`Failed to clean up file: ${err.message}`);
          });
      }

      if (request.body.fileUrls && Array.isArray(request.body.fileUrls)) {
        await Promise.all(
          request.body.fileUrls.map((path) =>
            this.storageService.deleteFileByPath(path).catch((err) => {
              this.logger.error(`Failed to clean up file: ${err.message}`);
            }),
          ),
        );
      }

      // Re-throw known exceptions; wrap unknown errors
      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException('File upload failed');
    }
  }

  private validateFile(file: Express.Multer.File): void {
    if (!file.originalname) {
      throw new BadRequestException('File name is required');
    }

    const extension = file.originalname.split('.').pop()?.toLowerCase();

    if (!extension || !this.allowedExtensions.includes(extension)) {
      throw new BadRequestException(
        `Invalid file extension. Allowed: ${this.allowedExtensions.join(', ')}`,
      );
    }
  }
}
