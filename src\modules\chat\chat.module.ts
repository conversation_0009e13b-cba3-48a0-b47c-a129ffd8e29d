import { Module } from '@nestjs/common';
import { CallsModule } from './calls/calls.module';
import { P2pModule } from './p2p/p2p.module';

/**
 * Chat Module
 *
 * Main module for chat functionality including peer-to-peer
 * messaging, voice/video calls, and real-time communication
 * features within the chat application.
 */
@Module({
  imports: [CallsModule, P2pModule],
  exports: [CallsModule, P2pModule],
})
export class ChatModule {}
