import { Injectable, Inject, OnModule<PERSON><PERSON>roy, Logger } from '@nestjs/common';
import {
  REDIS_CLIENT,
  FCM_TOKEN_KEY,
  ONLINE_PRESENCE_KEY,
  ONLINE_MEMBERS_HASH,
  REDIS_PUBLISHER,
  REDIS_SUBSCRIBER,
  REFRESH_TOKEN_KEY,
  REFRESH_TOKEN_BLACKLIST,
} from './constants/redis.constants';
import { RedisClient } from './interfaces/redis-client.interface';
import { UserType } from '../../common/types/user-type';
import { DeviceInfo, MemberStatus } from '../../common/types/presence.types';
import { Cron } from '@nestjs/schedule';

@Injectable()
export class RedisService implements OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private readonly presenceChannelName = 'presence-channel';
  private cleanupInterval: NodeJS.Timeout;

  constructor(
    @Inject(REDIS_CLIENT) private readonly redisClient: RedisClient,
    @Inject(REDIS_PUBLISHER) private readonly publisher: RedisClient,
    @Inject(REDIS_SUBSCRIBER) private readonly subscriber: RedisClient,
  ) {
    // Set up subscriber for presence channel
    this.setupSubscriber();
  }

  private setupSubscriber() {
    this.subscriber.subscribe(this.presenceChannelName, (err) => {
      if (err) {
        this.logger.error(
          `Failed to subscribe to presence channel: ${err.message}`,
        );
        return;
      }
      this.logger.log(`Subscribed to ${this.presenceChannelName}`);
    });

    this.subscriber.on('message', (channel, message) => {
      if (channel === this.presenceChannelName) {
        try {
          const data = JSON.parse(message);
          this.logger.debug(
            `Received presence update: ${JSON.stringify(data)}`,
          );
          // Handle presence event (can be extended based on your needs)
        } catch (e) {
          this.logger.error(`Error processing presence message: ${e.message}`);
        }
      }
    });
  }

  /**
   * Refresh Token Management
   */

  /**
   * Store a refresh token with user information in Redis
   * @param userType Type of user (product_admin or org_admin)
   * @param userId The user's ID
   * @param tokenId A unique ID for the token
   * @param refreshToken The refresh token to store
   * @param expiresIn Expiration time in seconds
   */
  /**
   * Store a refresh token with user information in Redis
   */
  async storeRefreshToken(
    userType: UserType,
    userId: number,
    tokenId: string,
    refreshToken: string,
    expiresIn: number,
  ): Promise<void> {
    const key = `${REFRESH_TOKEN_KEY}:${userType}:${userId}:${tokenId}`;

    await this.redisClient.set(key, refreshToken, 'EX', expiresIn);

    const metaKey = `${key}:meta`;
    await this.redisClient.hmset(metaKey, {
      createdAt: Date.now().toString(),
      userAgent: '',
      ip: '',
    });

    await this.redisClient.expire(metaKey, expiresIn);
  }

  /**
   * Retrieve refresh token by userType, userId and tokenId
   */
  async getRefreshToken(
    userType: UserType,
    userId: number,
    tokenId: string,
  ): Promise<string | null> {
    const key = `${REFRESH_TOKEN_KEY}:${userType}:${userId}:${tokenId}`;
    return this.redisClient.get(key);
  }

  /**
   * Remove a specific refresh token
   * @param userType Type of user
   * @param userId The user's ID
   * @param tokenId Token identifier
   */
  async removeRefreshToken(
    userType: UserType,
    userId: number,
    tokenId: string,
  ): Promise<void> {
    const key = `${REFRESH_TOKEN_KEY}:${userType}:${userId}:${tokenId}`;
    const metaKey = `${key}:meta`;

    await this.redisClient.del(key, metaKey);
  }

  /**
   * Remove all refresh tokens for a given user and type
   */
  async removeAllRefreshTokens(
    userType: UserType,
    userId: number,
  ): Promise<void> {
    const pattern = `${REFRESH_TOKEN_KEY}:${userType}:${userId}:*`;
    const keys = await this.redisClient.keys(pattern);

    if (keys.length) {
      const metaKeys = keys.map((key) => `${key}:meta`);
      await this.redisClient.del(...keys, ...metaKeys);
    }
  }

  /**
   * Blacklist a token
   */
  async blacklistToken(tokenId: string, expiresIn: number): Promise<void> {
    const key = `${REFRESH_TOKEN_BLACKLIST}:${tokenId}`;
    await this.redisClient.set(key, '1', 'EX', expiresIn);
  }

  /**
   * Check if token is blacklisted
   */
  async isTokenBlacklisted(tokenId: string): Promise<boolean> {
    const key = `${REFRESH_TOKEN_BLACKLIST}:${tokenId}`;
    return !!(await this.redisClient.exists(key));
  }

  /**
   * FCM Token Management
   */

  async storeFcmToken(
    memberId: number,
    token: string,
    deviceId: string,
  ): Promise<void> {
    const key = `${FCM_TOKEN_KEY}:${memberId}:${deviceId}`;
    await this.redisClient.set(key, token);
  }

  async getFcmTokens(memberId: number): Promise<string[]> {
    const pattern = `${FCM_TOKEN_KEY}:${memberId}:*`;
    const keys = await this.redisClient.keys(pattern);
    if (!keys.length) return [];

    const tokens = await this.redisClient.mget(...keys);
    return tokens.filter(Boolean) as string[];
  }

  async removeFcmToken(memberId: number, deviceId: string): Promise<void> {
    const key = `${FCM_TOKEN_KEY}:${memberId}:${deviceId}`;
    await this.redisClient.del(key);
  }

  async removeAllFcmTokens(memberId: number): Promise<void> {
    const pattern = `${FCM_TOKEN_KEY}:${memberId}:*`;
    const keys = await this.redisClient.keys(pattern);
    if (keys.length) {
      await this.redisClient.del(...keys);
    }
  }

  /**
   * Online Presence Management
   */
  async setMemberOnline(memberId: number, deviceId: string): Promise<void> {
    const deviceKey = `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`;
    const memberDevicesKey = `member_devices:${memberId}`;
    const now = Date.now().toString();

    try {
      // Set device-specific presence with TTL (2 minutes)
      await this.redisClient.set(deviceKey, now, 'EX', 45);

      // Track device under member's device set, set expire to 5 minutes
      await this.redisClient.sadd(memberDevicesKey, deviceId);
      await this.redisClient.expire(memberDevicesKey, 90);

      // Update overall member status
      await this.updateMemberOverallStatus(memberId);

      this.logger.log(`Member ${memberId} is online on device ${deviceId}`);
    } catch (error) {
      this.logger.error(`Error setting member ${memberId} online:`, error);
      throw error;
    }
  }

  async refreshPresence(memberId: number, deviceId: string): Promise<void> {
    const deviceKey = `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`;
    const memberDevicesKey = `member_devices:${memberId}`;
    const now = Date.now().toString();

    try {
      const exists = await this.redisClient.exists(deviceKey);
      if (!exists) {
        // If device presence expired, set member online again
        await this.setMemberOnline(memberId, deviceId);
        return;
      }

      // Refresh TTL and timestamp for device presence and member's devices set
      await this.redisClient.set(deviceKey, now, 'EX', 120);
      await this.redisClient.expire(memberDevicesKey, 300);

      // Update overall status
      await this.updateMemberOverallStatus(memberId);
    } catch (error) {
      this.logger.error(
        `Error refreshing presence for member ${memberId}:`,
        error,
      );
      throw error;
    }
  }

  async setMemberOffline(memberId: number, deviceId: string): Promise<void> {
    const deviceKey = `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`;
    const memberDevicesKey = `member_devices:${memberId}`;

    try {
      // Remove device presence and device from member set
      await this.redisClient.del(deviceKey);
      await this.redisClient.srem(memberDevicesKey, deviceId);

      // Update overall status after device removal
      await this.updateMemberOverallStatus(memberId);

      this.logger.log(`Member ${memberId} is offline on device ${deviceId}`);
    } catch (error) {
      this.logger.error(`Error setting member ${memberId} offline:`, error);
      throw error;
    }
  }

  private async updateMemberOverallStatus(memberId: number): Promise<void> {
    try {
      const memberDevicesKey = `member_devices:${memberId}`;
      const devices = await this.redisClient.smembers(memberDevicesKey);

      let overallStatus: 'online' | 'offline' | 'away' = 'offline';
      let latestActivity: number | null = null;
      let hasActiveDevices = false;
      let activePresences: number[] = [];
      let storedLastSeen: string | null = null;

      if (devices.length > 0) {
        const presenceChecks = devices.map((deviceId) =>
          this.redisClient.get(
            `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`,
          ),
        );

        const presences = await Promise.all(presenceChecks);

        activePresences = presences
          .filter((p) => p !== null)
          .map((p) => parseInt(p as string, 10))
          .filter((n) => !isNaN(n));

        if (activePresences.length > 0) {
          hasActiveDevices = true;
          overallStatus = 'online';
          latestActivity = Math.max(...activePresences);

          const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
          if (latestActivity < fiveMinutesAgo) {
            overallStatus = 'away';
          }
        }
      }

      // Update Redis hash with latest timestamp or remove if offline
      if (hasActiveDevices) {
        await this.redisClient.hset(
          ONLINE_MEMBERS_HASH,
          memberId.toString(),
          (latestActivity || Date.now()).toString(),
        );
      } else {
        // Try to fetch existing lastSeen before deleting the hash entry
        storedLastSeen = await this.redisClient.hget(
          ONLINE_MEMBERS_HASH,
          memberId.toString(),
        );

        await this.redisClient.hdel(ONLINE_MEMBERS_HASH, memberId.toString());
      }

      const statusMessage = {
        memberId,
        status: overallStatus,
        lastSeen: hasActiveDevices
          ? latestActivity
          : storedLastSeen
            ? parseInt(storedLastSeen, 10)
            : null,
        timestamp: Date.now().toString(),
        devices: devices.length,
        activeDevices: hasActiveDevices ? activePresences.length : 0,
      };

      await this.publisher.publish(
        this.presenceChannelName,
        JSON.stringify(statusMessage),
      );
    } catch (error) {
      this.logger.error(
        `Error updating overall status for member ${memberId}:`,
        error,
      );
    }
  }

  async getMemberStatus(memberId: number): Promise<MemberStatus> {
    try {
      const memberDevicesKey = `member_devices:${memberId}`;
      const devices = await this.redisClient.smembers(memberDevicesKey);

      if (devices.length === 0) {
        return {
          memberId,
          status: 'offline',
          devices: [],
          activeDevices: 0,
        };
      }

      const presenceChecks = devices.map((deviceId) =>
        this.redisClient.get(`${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`),
      );
      const presences = await Promise.all(presenceChecks);

      const activePresences = presences
        .filter((p) => p !== null)
        .map((p) => parseInt(p as string, 10))
        .filter((n) => !isNaN(n));

      if (activePresences.length === 0) {
        const lastSeenStr = await this.redisClient.hget(
          ONLINE_MEMBERS_HASH,
          memberId.toString(),
        );
        const lastSeen = lastSeenStr ? parseInt(lastSeenStr, 10) : undefined;

        return {
          memberId,
          status: 'offline',
          devices,
          activeDevices: 0,
          lastSeen,
        };
      }

      const latestActivity = Math.max(...activePresences);
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
      const status = latestActivity < fiveMinutesAgo ? 'away' : 'online';

      return {
        memberId,
        status,
        lastSeen: latestActivity,
        devices,
        activeDevices: activePresences.length,
      };
    } catch (error) {
      this.logger.error(`Error getting status for member ${memberId}:`, error);
      return {
        memberId,
        status: 'offline',
        devices: [],
        activeDevices: 0,
      };
    }
  }

  async getMultipleMemberStatuses(
    memberIds: number[],
  ): Promise<MemberStatus[]> {
    try {
      const statusPromises = memberIds.map((id) => this.getMemberStatus(id));
      return await Promise.all(statusPromises);
    } catch (error) {
      this.logger.error('Error getting multiple member statuses:', error);
      return memberIds.map((memberId) => ({
        memberId,
        status: 'offline',
        devices: [],
        activeDevices: 0,
      }));
    }
  }

  async getOnlineMembers(): Promise<number[]> {
    try {
      const entries = await this.redisClient.hgetall(ONLINE_MEMBERS_HASH);
      return Object.keys(entries).map((id) => parseInt(id, 10));
    } catch (error) {
      this.logger.error('Error getting online members:', error);
      return [];
    }
  }

  async isMemberOnline(memberId: number): Promise<boolean> {
    try {
      const exists = await this.redisClient.hexists(
        ONLINE_MEMBERS_HASH,
        memberId.toString(),
      );
      if (!exists) return false;

      const status = await this.getMemberStatus(memberId);
      return status.status === 'online' || status.status === 'away';
    } catch (error) {
      this.logger.error(
        `Error checking if member ${memberId} is online:`,
        error,
      );
      return false;
    }
  }

  async getMemberDevices(memberId: number): Promise<DeviceInfo> {
    try {
      const memberDevicesKey = `member_devices:${memberId}`;
      const devices = await this.redisClient.smembers(memberDevicesKey);

      if (devices.length === 0) {
        return { devices: [], activeDevices: [] };
      }

      const presenceChecks = devices.map(async (deviceId) => {
        const presence = await this.redisClient.get(
          `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`,
        );
        return { deviceId, active: presence !== null };
      });

      const deviceStatuses = await Promise.all(presenceChecks);
      const activeDevices = deviceStatuses
        .filter((d) => d.active)
        .map((d) => d.deviceId);

      return { devices, activeDevices };
    } catch (error) {
      this.logger.error(`Error getting devices for member ${memberId}:`, error);
      return { devices: [], activeDevices: [] };
    }
  }

  subscribeToPresenceChanges(callback: (message: string) => void): void {
    this.subscriber.on('message', (channel, message) => {
      if (channel === this.presenceChannelName) {
        try {
          callback(message);
        } catch (e) {
          this.logger.error(
            `Error in presence callback: ${(e as Error).message}`,
          );
        }
      }
    });
  }

  @Cron('*/1 * * * *')
  private async cleanupExpiredPresences(): Promise<void> {
    try {
      // Get all keys tracking member devices
      const memberDeviceKeys = await this.redisClient.keys('member_devices:*');

      for (const memberDeviceKey of memberDeviceKeys) {
        const memberId = parseInt(memberDeviceKey.split(':')[1], 10);
        const devices = await this.redisClient.smembers(memberDeviceKey);

        for (const deviceId of devices) {
          const presenceKey = `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`;
          const exists = await this.redisClient.exists(presenceKey);

          if (!exists) {
            // Remove expired device from member's device set
            await this.redisClient.srem(memberDeviceKey, deviceId);
          }
        }

        // Update member overall status after cleanup
        await this.updateMemberOverallStatus(memberId);
      }
    } catch (error) {
      this.logger.error('Error during presence cleanup:', error);
    }
  }

  async getPresenceDebugInfo(memberId: number): Promise<{
    memberDevices: string[];
    devicePresences: Array<{
      deviceId: string;
      timestamp: string | null;
      active: boolean;
    }>;
    overallStatus: MemberStatus;
    hashEntry: string | null;
  }> {
    try {
      const memberDevicesKey = `member_devices:${memberId}`;
      const memberDevices = await this.redisClient.smembers(memberDevicesKey);

      const devicePresences = await Promise.all(
        memberDevices.map(async (deviceId) => {
          const presenceKey = `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`;
          const timestamp = await this.redisClient.get(presenceKey);
          return {
            deviceId,
            timestamp,
            active: timestamp !== null,
          };
        }),
      );
      const overallStatus = await this.getMemberStatus(memberId);
      const hashEntry = await this.redisClient.hget(
        ONLINE_MEMBERS_HASH,
        memberId.toString(),
      );

      return {
        memberDevices,
        devicePresences,
        overallStatus,
        hashEntry,
      };
    } catch (error) {
      this.logger.error(
        `Error getting debug info for member ${memberId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Cleanup when the module is destroyed
   */
  async onModuleDestroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    await this.redisClient.quit();
    await this.publisher.quit();
    await this.subscriber.quit();
  }
}
