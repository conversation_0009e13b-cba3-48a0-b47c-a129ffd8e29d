events {
    worker_connections 1024;
}

http {
    # Add basic HTTP settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Upstream backend services
    upstream chat-frontend {
        server chat-admin-web:3001;
    }

    upstream chat-backend {
        server nest-backend:3000;
    }

    upstream assets-frontend {
        server assets-frontend:3003;
    }

    upstream assets-backend {
        server assets-backend:3002;
    }

    ############################
    # MAIN CHAT APP - HTTP ONLY
    ############################
    server {
        listen 80;
        # No domain yet, just IP access for chat app

        # Chat frontend
        location / {
            proxy_pass http://chat-frontend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Chat backend REST API
        location /chat/api/ {
            proxy_pass http://chat-backend/api/v1/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket endpoint (no prefix on backend)
        location /chat/socket.io/ {
            proxy_pass http://chat-backend/socket.io/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # WebSocket support
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
        }
    }

    ##########################################
    # CHAT APP DOMAIN - HTTP (for certbot) and HTTPS
    ##########################################

    # HTTP server for chat domain - handle Let's Encrypt and redirect
    server {
        listen 80;
        server_name talkio.invictainnovations.com;

        # Let's Encrypt ACME Challenge for certificate verification
        location /.well-known/acme-challenge/ {
            root /var/www/html;
        }

        # Redirect all other HTTP requests to HTTPS
        location / {
            return 301 https://$server_name$request_uri;
        }
    }

    # HTTPS server for chat domain
    server {
        listen 443 ssl;
        http2 on;
        server_name talkio.invictainnovations.com;

        ssl_certificate /etc/letsencrypt/live/talkio.invictainnovations.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/talkio.invictainnovations.com/privkey.pem;

        # Optional SSL optimizations (recommended for production)
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-RSA-AES128-GCM-SHA256;
        ssl_prefer_server_ciphers off;

        # Chat frontend (root path)
        location / {
            proxy_pass http://chat-frontend/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Chat backend REST API
        location /chat/api/ {
            proxy_pass http://chat-backend/api/v1/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket endpoint (no prefix on backend)
        location /chat/socket.io/ {
            proxy_pass http://chat-backend/socket.io/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # WebSocket support
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 86400;
        }
    }

    ##########################################
    # ASSETS SUBDOMAIN - HTTP (for certbot) and HTTPS
    ##########################################


    # HTTP server for assets subdomain - handle Let's Encrypt and redirect
    server {
        listen 80;
        server_name ams.invictainnovations.com;

        # Let's Encrypt ACME Challenge for certificate verification
        location /.well-known/acme-challenge/ {
            root /var/www/html;
        }

        # Redirect all other HTTP requests to HTTPS
        location / {
            return 301 https://$server_name$request_uri;
        }
    }

    # HTTPS server for assets subdomain
    server {
        listen 443 ssl;
        http2 on;
        server_name ams.invictainnovations.com;

        ssl_certificate /etc/letsencrypt/live/ams.invictainnovations.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/ams.invictainnovations.com/privkey.pem;

        # Optional SSL optimizations (recommended for production)
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-RSA-AES128-GCM-SHA256;
        ssl_prefer_server_ciphers off;

        # Proxy Asset Frontend (root path)
        location / {
            proxy_pass http://assets-frontend/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Proxy Asset Backend API
        location /assets/api/ {
            proxy_pass http://assets-backend/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}

