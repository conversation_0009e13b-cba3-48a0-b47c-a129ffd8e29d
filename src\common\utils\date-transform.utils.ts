export function formatISOToReadable(
  isoDateString: string,
  dateFormat: string = 'dd/MM/yyyy',
): string {
  // Parse the ISO date string
  const date = new Date(isoDateString);

  // Get date components
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
  const year = date.getFullYear();

  // Get time components
  let hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const ampm = hours >= 12 ? 'PM' : 'AM';

  // Convert hours to 12-hour format
  hours = hours % 12;
  hours = hours ? hours : 12; // Convert '0' to '12'
  const hoursStr = String(hours).padStart(2, '0');

  // Format the date according to the specified format
  let formattedDate = dateFormat
    .replace('dd', day)
    .replace('MM', month)
    .replace('yyyy', String(year));

  // Combine date and time
  return `${formattedDate} ${hoursStr}:${minutes} ${ampm}`;
}
