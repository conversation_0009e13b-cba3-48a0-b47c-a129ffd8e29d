import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MemberFcmToken } from './entities/member-fcm-token.entity';
import { MembersService } from './services/members.service';
import { MembersController } from './controllers/members.controller';
import { StorageModule } from '../../core/storage/storage.module';
import { EncryptionModule } from '../../core/encryption/encryption.module';
import { SocketModule } from '../../infrastructure/socket/socket.module';
import { OrgMember } from './entities/org-member.entity';
import { UserModule } from '../users/users.module';
import { OrganizationsModule } from '../organization/organizations.module';
import { CoreModule } from '../../core/core.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([OrgMember, MemberFcmToken]),
    CoreModule,
    UserModule,
    OrganizationsModule,
    forwardRef(() => SocketModule),
  ],
  providers: [MembersService],
  controllers: [MembersController],
  exports: [MembersService],
})
export class MembersModule {}
