import {
  ArgumentsHost,
  BadRequestException,
  Catch,
  ExceptionFilter,
  ForbiddenException,
  HttpException,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ValidationError } from 'class-validator';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = 500;
    let message = 'Internal server error';
    let errorName = 'UnknownError';
    let stack: string | undefined;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const responseBody = exception.getResponse();
      message =
        typeof responseBody === 'string'
          ? responseBody
          : (responseBody as any)?.message || message;
      errorName = exception.name;
      stack = exception.stack;

      // Specific logging for common HTTP errors
      if (
        exception instanceof UnauthorizedException ||
        exception instanceof ForbiddenException ||
        exception instanceof NotFoundException ||
        exception instanceof BadRequestException
      ) {
        this.logger.warn(
          `${request.method} ${request.url} ${status} ${message}`,
        );
      } else {
        this.logger.error(
          `${request.method} ${request.url} ${status} ${message}`,
        );
      }
    } else if (exception instanceof Error) {
      message = exception.message || message;
      errorName = exception.name;
      stack = exception.stack;
      this.logger.error(
        `Unhandled error at ${request.method} ${request.url}`,
        stack,
      );
    } else if (
      Array.isArray(exception) &&
      exception[0] instanceof ValidationError
    ) {
      message = exception
        .map((e) => Object.values(e.constraints || {}).join(', '))
        .join('; ');
      errorName = 'ValidationError';
      this.logger.warn(
        `${request.method} ${request.url} 400 Validation failed: ${message}`,
      );
      status = 400;
    } else {
      this.logger.error(
        `Unknown error type at ${request.method} ${request.url}`,
        JSON.stringify(exception),
      );
    }

    response.status(status).json({
      statusCode: status,
      message,
      error: errorName,
      timestamp: new Date().toISOString(),
      path: request.url,
    });
  }
}
