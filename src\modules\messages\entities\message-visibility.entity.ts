import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  DeleteDateColumn,
} from 'typeorm';
import { Message } from './message.entity';
import { OrgMember } from '../../members/entities/org-member.entity';

@Entity('message_visibility')
export class MessageVisibility {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'message_id' })
  messageId: number;

  @Column({ name: 'member_id' })
  memberId: number;

  @Column({ name: 'is_visible', default: true })
  isVisible: boolean;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt: Date;

  // Relations
  @ManyToOne(() => Message, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'message_id' })
  message: Message;

  @ManyToOne(() => OrgMember, (orgMember) => orgMember.messageVisibilities)
  @JoinColumn({ name: 'member_id' })
  member: OrgMember;
}
