import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CallQualityService } from './services/call-quality.service';
import { CallRecordingService } from './services/call-recording.service';
import { CallSignalingService } from './services/call-signaling.service';
import { TurnService } from './services/turn.service';
import { WebrtcService } from './services/webrtc.service';
import { CallsController } from './controllers/calls.controller';
import { WebrtcController } from './controllers/webrtc.controller';

/**
 * Calls Module
 *
 * Handles voice and video calling functionality including
 * WebRTC signaling, call quality monitoring, recording,
 * and TURN server management.
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Add call-related entities here when created
    ]),
  ],
  controllers: [CallsController, WebrtcController],
  providers: [
    CallQualityService,
    CallRecordingService,
    CallSignalingService,
    TurnService,
    WebrtcService,
  ],
  exports: [
    CallQualityService,
    CallRecordingService,
    CallSignalingService,
    TurnService,
    WebrtcService,
  ],
})
export class CallsModule {}
