export const getOrganizationGroupAllocationTemplate = (
  memberName: string,
  organizationName: string,
  groupName: string,
  chatAppName: string,
  phoneNumber: string,
) => `
      <!DOCTYPE html>
      <html>
      <head>
          <style>
              .email-container {
                  max-width: 600px;
                  margin: 0 auto;
                  font-family: Arial, sans-serif;
                  padding: 20px;
                  background-color: #f7f7f7;
              }
              .header-table {
                  width: 100%;
                  background-color: #4A90E2;
                  border-radius: 5px 5px 0 0;
              }
              .header-cell {
                  padding: 20px;
                  text-align: center;
              }
              .header-logo {
                  max-width: 100px;
              }
              .content {
                  background-color: white;
                  padding: 20px;
                  border-radius: 0 0 5px 5px;
                  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
              }
              .group-details-container {
                  margin: 20px 0;
                  padding: 20px;
                  background: #f8f9fa;
                  border-radius: 5px;
                  border-left: 4px solid #4A90E2;
              }
              .detail-item {
                  margin: 10px 0;
                  padding: 10px;
                  background: white;
                  border-radius: 3px;
                  border: 1px solid #e9ecef;
              }
              .detail-label {
                  color: #666;
                  font-size: 14px;
                  margin-bottom: 5px;
              }
              .detail-value {
                  font-size: 16px;
                  font-weight: bold;
                  color: #4A90E2;
                  word-break: break-all;
              }
              .instructions {
                  background-color: #e8f4fd;
                  border: 1px solid #b8daff;
                  color: #004085;
                  padding: 12px;
                  border-radius: 4px;
                  margin: 20px 0;
                  font-size: 14px;
              }
              .download-section {
                  margin: 20px 0;
                  padding: 15px;
                  background-color: #f2f2f2;
                  border-radius: 5px;
                  text-align: center;
              }
              .download-button {
                  display: inline-block;
                  margin: 10px;
                  padding: 10px 20px;
                  background-color: #4A90E2;
                  color: white;
                  text-decoration: none;
                  border-radius: 5px;
                  font-weight: bold;
              }
              .important-note {
                  color: #721c24;
                  background-color: #f8d7da;
                  border: 1px solid #f5c6cb;
                  padding: 12px;
                  border-radius: 4px;
                  margin: 20px 0;
                  font-size: 14px;
              }
              .footer {
                  text-align: center;
                  margin-top: 20px;
                  color: #666;
                  font-size: 12px;
                  padding: 20px;
                  border-top: 1px solid #eee;
              }
              .action-required {
                  background-color: #4A90E2;
                  color: white;
                  padding: 8px 16px;
                  border-radius: 4px;
                  display: inline-block;
                  margin: 10px 0;
                  font-weight: bold;
              }
              .organization-name {
                  color: #4A90E2;
                  font-weight: bold;
              }
              .group-name {
                  color: #4A90E2;
                  font-weight: bold;
              }
          </style>
      </head>
      <body>
          <div class="email-container">
              <table class="header-table" role="presentation" cellpadding="0" cellspacing="0">
                  <tr>
                      <td class="header-cell">
                          <h2 style="color: white; margin: 10px 0 0;">You've Been Added to a Group!</h2>
                      </td>
                  </tr>
              </table>
              <div class="content">                  
                  <p>Hello ${memberName},</p>
                  
                  <p>Congratulations! You have been added to the <span class="group-name">${groupName}</span> group in <span class="organization-name">${organizationName}</span>. You can now connect and interact with other members of this group through our chat application.</p>
                  
                  <div class="group-details-container">
                      <div class="detail-item">
                          <div class="detail-label">Organization</div>
                          <div class="detail-value">${organizationName}</div>
                      </div>
                      <div class="detail-item">
                          <div class="detail-label">Group</div>
                          <div class="detail-value">${groupName}</div>
                      </div>
                      <div class="detail-item">
                          <div class="detail-label">Your Registered Phone Number</div>
                          <div class="detail-value">${phoneNumber}</div>
                      </div>
                  </div>
      
                  <div class="action-required">
                      Action Required: Download ${chatAppName}
                  </div>
                  
                  <div class="instructions">
                      <strong>Getting Started:</strong><br>
                      1. Download ${chatAppName} from your app/play store<br>
                      2. Open the app and verify your identity using your phone number: ${phoneNumber}<br>
                      3. Once verified, you'll automatically have access to your group
                  </div>
                  
                  <div class="download-section">
                      <p>Download ${chatAppName} now:</p>
                      <a href="https://play.google.com/store/apps/details?id=com.invicta.talkio" class="download-button">Google Play Store</a>
                      <a href="https://apps.apple.com/app/${chatAppName.toLowerCase().replace(/\s/g, '')}" class="download-button">Apple App Store</a>
                  </div>
                  
                  <div class="important-note">
                      <strong>Important:</strong> Your phone number is used for authentication purposes. Please ensure you have access to this phone number when setting up your account.
                  </div>
              </div>
              <div class="footer">
                  <p>This is an automated message, please do not reply.</p>
                  <p>If you believe you've received this message in error, please contact your organization administrator.</p>
                  <p>&copy; ${new Date().getFullYear()} ${chatAppName}. All rights reserved.</p>
              </div>
          </div>
      </body>
      </html>
    `;
