import {
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  Res,
  Body,
  Get,
  Param,
  ParseIntPipe,
  UseInterceptors,
  Query,
  Req,
  UnauthorizedException,
  Put,
  NotFoundException,
  BadRequestException,
  Delete,
} from '@nestjs/common';
import { CreateGroupDto } from '../dto/create-group.dto';
import { Request, Response } from 'express';
import { GroupsService } from '../services/groups.service';
import { Group } from '../entities/group.entity';
import { AllocateMemberDto } from '../dto/allocate-member.dto';
import { GroupMember } from '../entities/group-member.entity';
import { FileInterceptor } from '@nestjs/platform-express';
import { CloudStorageInterceptor } from '../../../common/interceptors/cloud-storage.interceptor';
import { ImageUrlInterceptor } from '../../../common/interceptors/image-url.interceptor';
import { UpdateGroupDto } from '../dto/update-group.dto';
import { UpdateGroupMemberDto } from '../dto/update.groupmember.dto';

interface GroupWithIsActive extends Group {
  isActive: boolean;
}

@Controller('groups')
export class GroupsController {
  constructor(private readonly groupsService: GroupsService) {}

  @Post('create/:userId')
  @UseInterceptors(FileInterceptor('image'), CloudStorageInterceptor)
  @HttpCode(HttpStatus.OK)
  async create(
    @Param('userId') userId: string,
    @Body() createGroupDto: CreateGroupDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<Group> {
    const user = request.user;

    if (Number(user.id) !== Number(userId)) {
      throw new UnauthorizedException();
    }

    const group = await this.groupsService.create(createGroupDto, userId);

    response.locals.message = 'Group created successfully';

    return group;
  }

  @Post('allocate-members/:userId')
  @HttpCode(HttpStatus.OK)
  async allocateMembersToGroup(
    @Param('userId') userId: string,
    @Body() allocateMemberDto: AllocateMemberDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<GroupMember[]> {
    const user = request.user;

    if (Number(user.id) !== Number(userId)) {
      throw new UnauthorizedException();
    }

    const allocatedMembers = await this.groupsService.allocateMembersToGroup(
      allocateMemberDto,
      userId,
    );

    response.locals.message = 'Members allocated to group successfully';

    return allocatedMembers;
  }

  @Get('organization')
  @UseInterceptors(ImageUrlInterceptor)
  @HttpCode(HttpStatus.OK)
  async getGroupsByOrgId(
    @Query('orgId', ParseIntPipe) orgId: number,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<GroupWithIsActive[]> {
    const user = request.user;

    if (user.orgId !== Number(orgId)) {
      throw new UnauthorizedException();
    }

    const groups = await this.groupsService.findAllByOrgId(orgId);

    const result = groups.map((group: Group) => ({
      ...group,
      isActive: group.deletedAt === null,
    }));

    response.locals.message = 'Groups with members fetched successfully';
    return result;
  }

  @Get('group-members')
  @UseInterceptors(ImageUrlInterceptor)
  @HttpCode(HttpStatus.OK)
  async getAllGroupsWithMembers(
    @Query('orgId', ParseIntPipe) orgId: number,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ) {
    const user = request.user;

    if (user.orgId !== Number(orgId)) {
      throw new UnauthorizedException();
    }
    const result = await this.groupsService.getAllGroupsWithMembers(orgId);
    response.locals.message = 'Group members fetched successfully';
    return result;
  }

  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('image'), CloudStorageInterceptor)
  async updateGroup(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateGroupDto: UpdateGroupDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<Group> {
    const user = request.user;

    // 🔍 Find the group first
    const group = await this.groupsService.findOne(id);
    if (!group) {
      throw new NotFoundException('Group not found');
    }

    // 🔐 Check if the current user is the creator of the group
    if (group.createdBy !== user.id) {
      throw new UnauthorizedException();
    }

    const result = await this.groupsService.updateGroup(
      id,
      updateGroupDto,
      user.id,
    );
    response.locals.message = 'Group updated successfully';
    return result;
  }

  @Put(':userId/update-members')
  async updateGroupMembers(
    @Param('userId') userId: string,
    @Req() req: Request,
    @Body() updateGroupMemberDto: UpdateGroupMemberDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    (req as any).res.locals.message = 'Group members updated successfully';

    const user = req.user;

    if (Number(userId) !== Number(user.id)) {
      throw new UnauthorizedException();
    }

    return await this.groupsService.updateGroupMembers(
      updateGroupMemberDto,
      userId,
    );
  }

  @Delete(':id')
  async deleteGroup(
    @Param('id', ParseIntPipe) id: number,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<{ message: string }> {
    const user = request.user;

    // 🔍 Find the group first
    const group = await this.groupsService.findOne(id);
    if (!group) {
      throw new NotFoundException('Group not found');
    }

    // 🔐 Check if the current user is the creator of the group
    if (group.createdBy !== user.id) {
      throw new UnauthorizedException();
    }

    await this.groupsService.deleteGroup(id, user.id);
    response.locals.message = 'Group deleted successfully';
    return { message: 'Group deleted successfully' };
  }
}
