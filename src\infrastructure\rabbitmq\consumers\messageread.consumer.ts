// src/consumers/message-read.consumer.ts
import { Injectable } from '@nestjs/common';
import { RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { SocketGateway } from '../../socket/socket.gateway';
import { MessageReadDto } from '../../../modules/messages/dto/message-read.dto';

@Injectable()
export class MessageReadConsumer {
  constructor(private readonly socketGateway: SocketGateway) {}

  @RabbitSubscribe({
    exchange: 'message_exchange',
    routingKey: 'message.read',
    queue: 'message_read_queue',
  })
  async handleMessageRead(msg: MessageReadDto) {
    const { groupId, readerId, messages } = msg;

    this.socketGateway.server.to(String(groupId)).emit('message_read_update', {
      groupId,
      readerId,
      messages,
      unreadCount: 0,
    });
  }
}
