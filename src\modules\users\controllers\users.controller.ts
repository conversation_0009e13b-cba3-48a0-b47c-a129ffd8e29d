import {
  Body,
  Controller,
  Delete,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Req,
  Res,
  UnauthorizedException,
  UseInterceptors,
} from '@nestjs/common';
import { UsersService } from '../services/users.service';
import { ConfigService } from '@nestjs/config';
import { CreateUserDto } from '../dto/create-users.dto';
import { User } from '../entities/user.entity';
import { Response, Request } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { CloudStorageInterceptor } from '../../../common/interceptors/cloud-storage.interceptor';
import { Headers } from '@nestjs/common';
import { UpdateUserDto } from '../dto/update.users.dto';
import { ChangePasswordDto } from '../dto/change-password.dto';
import { UpdateOrganizationDto } from '../../organization/dto';
import { Organization } from '../../organization/entities/organization.entity';

@Controller('users')
export class UserController {
  constructor(
    private readonly usersService: UsersService,
    private readonly configService: ConfigService,
  ) {}

  @Post('create-admin')
  @UseInterceptors(FileInterceptor('image'), CloudStorageInterceptor)
  @HttpCode(HttpStatus.OK)
  async createWithOrganization(
    @Body() createUserDto: CreateUserDto,
    @Res({ passthrough: true }) response: Response,
    @Headers('x-storage-folder') storageFolder?: string,
  ): Promise<Partial<User>> {
    console.log('kkk', { createUserDto });

    const user = await this.usersService.createWithOrganization(createUserDto);

    response.locals.message = 'Admin created successfully';

    // Remove password from response
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  @Put(':id')
  @UseInterceptors(FileInterceptor('image'), CloudStorageInterceptor)
  @HttpCode(HttpStatus.OK)
  async updateUser(
    @Param('id', ParseIntPipe) id: number,
    @Res({ passthrough: true }) response: Response,
    @Body() updateUserDto: UpdateUserDto,
    @Req() request: Request,
  ): Promise<Partial<User>> {
    const user = request.user;

    // 🔐 Check if the current user is the creator of the group
    if (user.id !== id) {
      throw new UnauthorizedException();
    }

    const result = await this.usersService.update(id, updateUserDto);

    response.locals.message = 'User Updated successfully';

    const { password, ...userWithoutPassword } = result;
    return userWithoutPassword;
  }

  @Put(':id/change-password')
  @HttpCode(HttpStatus.OK)
  async changePassword(
    @Param('id', ParseIntPipe) id: number,
    @Body() changePasswordDto: ChangePasswordDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<Partial<User> | { message: string }> {
    const user = request.user;
    if (user.id !== id) {
      throw new UnauthorizedException();
    }
    const result = await this.usersService.changePassword(
      id,
      changePasswordDto,
    );
    response.locals.message = 'Password changed successfully';
    return result;
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  async deleteUser(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ message: string }> {
    return this.usersService.remove(id);
  }

  @Put('organizations/:org_id')
  @UseInterceptors(FileInterceptor('image'), CloudStorageInterceptor)
  @HttpCode(HttpStatus.OK)
  update(
    @Param('org_id', ParseIntPipe) id: number,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<Organization> {
    const user = request.user;

    if (user.roleId !== 1) {
      throw new UnauthorizedException();
    }

    response.locals.message = 'Organization updated successfully';
    return this.usersService.updateWithOrganization(id, updateOrganizationDto);
  }
}
