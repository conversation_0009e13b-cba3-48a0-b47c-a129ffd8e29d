import { Module } from '@nestjs/common';
import { EncryptionModule } from './encryption/encryption.module';
import { MailModule } from './mail/mail.module';
import { SmsModule } from './sms/sms.module';
import { StorageModule } from './storage/storage.module';

@Module({
  imports: [EncryptionModule, MailModule, SmsModule, StorageModule],
  exports: [EncryptionModule, MailModule, SmsModule, StorageModule],
})
export class CoreModule {}
