import { Catch, ExceptionFilter, ArgumentsHost, Logger } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';

@Catch()
export class RabbitMQExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(RabbitMQExceptionFilter.name);

  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToRpc();
    const data = ctx.getData();

    // Check if it's a token/rate limit related error
    const isTokenError = this.isTokenRelatedError(exception);
    const isRateLimitError = this.isRateLimitError(exception);

    if (isTokenError || isRateLimitError) {
      // Log once with structured info instead of infinite logging
      this.logger.warn({
        message: 'SMS service temporarily unavailable',
        error: isTokenError ? 'Token expired/invalid' : 'Rate limit exceeded',
        phoneNumber: data?.phoneNumber,
        timestamp: new Date().toISOString(),
        willRetryLater: true,
      });

      // Don't throw - this will prevent infinite retries
      // The message will go to DLQ after max retries
      return;
    }

    // For other errors, log and rethrow
    this.logger.error({
      message: 'Unexpected error in RabbitMQ consumer',
      error: exception.message,
      stack: exception.stack,
      data,
    });

    throw new RpcException(exception);
  }

  private isTokenRelatedError(exception: any): boolean {
    const errorMessage = exception?.message?.toLowerCase() || '';
    const errorResponse =
      exception?.response?.data?.message?.toLowerCase() || '';

    return (
      errorMessage.includes('token') ||
      errorMessage.includes('unauthorized') ||
      errorMessage.includes('authentication') ||
      errorResponse.includes('token') ||
      errorResponse.includes('unauthorized') ||
      exception?.response?.status === 401
    );
  }

  private isRateLimitError(exception: any): boolean {
    const errorMessage = exception?.message?.toLowerCase() || '';
    const errorResponse =
      exception?.response?.data?.message?.toLowerCase() || '';

    return (
      errorMessage.includes('limit') ||
      errorMessage.includes('quota') ||
      errorMessage.includes('throttle') ||
      errorResponse.includes('limit') ||
      errorResponse.includes('quota') ||
      exception?.response?.status === 429
    );
  }
}
