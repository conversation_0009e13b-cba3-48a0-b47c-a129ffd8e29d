import { Injectable } from '@nestjs/common';
import { RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { SmsService } from '../../../core/sms/sms.service';

@Injectable()
export class OtpConsumer {
  constructor(private readonly smsService: SmsService) {}

  @RabbitSubscribe({
    exchange: 'otp_exchange',
    routingKey: 'otp.send',
    queue: 'otp_queue',
  })
  async handleOtpSend(message: { phoneNumber: string; otp: string }) {
    try {
      await this.smsService.sendSmsVerification(
        message.phoneNumber,
        message.otp,
      );
      console.log(`OTP sent successfully to ${message.phoneNumber}`);
    } catch (err) {
      console.error(`Failed to send OTP to ${message.phoneNumber}:`, err);
      // Consider implementing retry logic or dead letter queue here
      throw err;
    }
  }
}
