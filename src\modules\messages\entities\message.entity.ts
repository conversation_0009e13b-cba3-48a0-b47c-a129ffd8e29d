import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Group } from '../../groups/entities/group.entity';
import { OrgMember } from '../../members/entities/org-member.entity';

@Entity('messages')
export class Message {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'group_id' })
  groupId: number;

  @Column({ name: 'sender_id' })
  senderId: number;

  @Column({ name: 'encrypted_content', nullable: true })
  encryptedContent: string;

  @Column({ name: 'nonce', nullable: true })
  nonce: string;

  @Column({ name: 'group_key_version' })
  groupKeyVersion: number;

  @Column({ name: 'sent_at' })
  sentAt: Date;

  @Column({ name: 'encrypted_meta_data', type: 'jsonb', nullable: true })
  encryptedMetaData: Record<string, any>;

  @Column({ name: 'is_deleted', default: false })
  isDeleted: boolean;

  @Column({ name: 'reply_to_message_id', nullable: true })
  replyToMessageId: number;

  @Column({ name: 'file_id', nullable: true })
  fileId: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => Group)
  @JoinColumn({ name: 'group_id' })
  group: Group;

  @ManyToOne(() => OrgMember)
  @JoinColumn({ name: 'sender_id' })
  sender: OrgMember;

  @ManyToOne(() => Message, { nullable: true })
  @JoinColumn({ name: 'reply_to_message_id' })
  replyTo: Message;

  // Additional properties for relations
  file: any;
  reads: any[];
  visibilities: any[];
  content: string; // Alias for encryptedContent
  date: Date; // Alias for sentAt
}
