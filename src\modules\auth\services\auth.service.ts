import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
  BadRequestException,
  Logger,
  Inject,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';
import * as argon2 from 'argon2';
import { RedisService } from '../../../infrastructure/redis/redis.service';
import { JwtPayload } from '../interfaces/jwt-payload.interface';
import { RefreshTokenPayload } from '../interfaces/refresh-token-payload.interface';
import { AdminLoginDto, AdminResponseDto, LoginResponseDto } from '../dto/';
import { UserType } from '../../../common/types/user-type';
import { UsersService } from '../../users/services/users.service';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  constructor(
    @Inject(UsersService)
    private readonly userService: UsersService,
    private jwtService: JwtService,
    private configService: ConfigService,
    private redisService: RedisService,
  ) {}

  /**
   * Authenticate an admin user
   */
  async login(loginDto: AdminLoginDto): Promise<LoginResponseDto> {
    // Find the user by username (assumed to be unique)
    const user = await this.userService.findOne(loginDto.username);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Determine user type based on roleId
    let userType: 'product_admin' | 'org_admin';
    let orgName: string | undefined;

    if (user.roleId === 1) {
      userType = 'product_admin';
    } else {
      userType = 'org_admin';
      if (user.organization) {
        orgName = user.organization.name;
      }
    }

    // Validate password
    const isPasswordValid = await this.verifyPassword(
      user.password,
      loginDto.password,
    );

    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Generate tokens
    const { accessToken, refreshToken, tokenId } = await this.generateTokens({
      sub: user.id,
      email: user.email,
      type: userType,
      orgId: userType === 'org_admin' ? user.organization?.id : undefined,
    });

    // Calculate expiration for refresh token
    const refreshTokenExpiresIn =
      parseInt(
        this.configService
          .get<string>('jwt.refreshExpiresIn', '7d')
          .replace('d', '') || '7',
      ) *
      24 *
      60 *
      60;

    // Store refresh token in Redis
    await this.redisService.storeRefreshToken(
      userType,
      user.id,
      tokenId,
      refreshToken,
      refreshTokenExpiresIn,
    );

    // Return user details and tokens
    return {
      accessToken,
      refreshToken,
    };
  }

  async getUserDetails(user): Promise<AdminResponseDto | any> {
    if ('username' in user) {
      // It's an admin
      const type = user.roleId === 1 ? 'product_admin' : 'org_admin';

      let encryptedAdminSecretKey: string | undefined;
      let adminSecretKeyNonce: string | undefined;
      let adminSecretKeySalt: string | undefined;

      if (user.roleId === 2) {
        // Assuming your user entity now has these fields
        encryptedAdminSecretKey = user.encryptedAdminSecretKey;
        adminSecretKeyNonce = user.adminSecretKeyNonce;
        adminSecretKeySalt = user.adminSecretKeySalt;
      }

      const response: AdminResponseDto = {
        id: user.id,
        username: user.username,
        email: user.email,
        type,
        roleId: user.roleId,
        orgId: user.organization?.id,
        orgName: user.organization?.name,
        imageUrl: user?.imageUrl,
        ...(user.roleId === 2 && {
          encryptedAdminSecretKey,
          adminSecretKeyNonce,
          adminSecretKeySalt,
        }),
      };

      return response;
    }

    const admin = await this.userService.findByRoleId(2, user.orgId);

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      orgId: user.organization.id,
      orgName: user.organization.name,
      type: 'org_member',
      imageUrl: user?.imageUrl,
      encryptedPrivateKey: user.encryptedPrivateKey,
      publicKey: admin?.publicKey,
    };
  }

  /**
   * Refresh access token using a valid refresh token
   */
  async refreshToken(
    refreshToken: string,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // Verify the refresh token
      const decoded = await this.jwtService.verifyAsync<RefreshTokenPayload>(
        refreshToken,
        {
          secret: this.configService.get<string>('jwt.refreshSecret'),
        },
      );

      const { sub, tokenId, type } = decoded;

      // Check if token is blacklisted
      const isBlacklisted = await this.redisService.isTokenBlacklisted(tokenId);
      if (isBlacklisted) {
        throw new UnauthorizedException('Refresh token is invalid');
      }

      // Verify token exists in Redis
      const storedToken = await this.redisService.getRefreshToken(
        type,
        sub,
        tokenId,
      );

      if (!storedToken || storedToken !== refreshToken) {
        throw new UnauthorizedException('Refresh token is invalid');
      }

      // Remove the old refresh token
      await this.redisService.removeRefreshToken(type, sub, tokenId);

      // Generate new tokens
      const {
        accessToken,
        refreshToken: newRefreshToken,
        tokenId: newTokenId,
      } = await this.generateTokens({
        sub,
        email: decoded.email,
        orgId: decoded.orgId,
        type,
      });

      // Store new refresh token in Redis
      const refreshTokenExpiresIn =
        parseInt(
          this.configService
            .get<string>('jwt.refreshExpiresIn', '7d')
            .replace('d', '') || '7',
        ) *
        24 *
        60 *
        60;

      await this.redisService.storeRefreshToken(
        type,
        sub,
        newTokenId,
        newRefreshToken,
        refreshTokenExpiresIn,
      );

      return {
        accessToken,
        refreshToken: newRefreshToken,
      };
    } catch (error) {
      this.logger.error(`Refresh token error: ${error.message}`);
      throw new UnauthorizedException('Could not refresh token');
    }
  }

  /**
   * Logout an admin user by blacklisting their refresh token
   */
  async logout(
    userId: number,
    userType: string,
    tokenId: string,
  ): Promise<void> {
    try {
      // Get refresh token expiration for blacklist TTL
      const refreshExpiresInDays = this.configService.get<string>(
        'jwt.refreshExpiresIn',
        '7d',
      );
      const refreshExpiresIn =
        parseInt(refreshExpiresInDays.replace('d', '') || '7') * 24 * 60 * 60;
      // Add token to blacklist
      await this.redisService.blacklistToken(tokenId, refreshExpiresIn);
      // Remove the specific refresh token
      await this.redisService.removeRefreshToken(
        userType as UserType,
        userId,
        tokenId,
      );
    } catch (error) {
      this.logger.error(`Logout error: ${error.message}`);
      throw new BadRequestException('Could not complete logout');
    }
  }

  /**
   * Logout from all devices by removing all refresh tokens for a user
   */
  async logoutAll(userId: number, userType: string): Promise<void> {
    try {
      await this.redisService.removeAllRefreshTokens(
        userType as UserType,
        userId,
      );
    } catch (error) {
      this.logger.error(`Logout all error: ${error.message}`);
      throw new BadRequestException(
        'Could not complete logout from all devices',
      );
    }
  }

  async decodeRefreshToken(refreshToken: string): Promise<RefreshTokenPayload> {
    try {
      return await this.jwtService.verifyAsync<RefreshTokenPayload>(
        refreshToken,
        {
          secret: this.configService.get<string>('jwt.refreshSecret'),
        },
      );
    } catch (error) {
      this.logger.error(`Refresh token decode error: ${error.message}`);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  /**
   * Generate JWT tokens (access and refresh)
   */
  async generateTokens(payload: Omit<JwtPayload, 'iat' | 'exp'>) {
    const tokenId = uuidv4(); // Generate unique token ID
    // Create access token
    const accessToken = await this.jwtService.signAsync(payload, {
      secret: this.configService.get<string>('jwt.secret'),
      expiresIn: this.configService.get<string>('jwt.expiresIn', '1d'),
    });
    // Create refresh token with tokenId
    const refreshToken = await this.jwtService.signAsync(
      {
        ...payload,
        tokenId,
      },
      {
        secret: this.configService.get<string>('jwt.refreshSecret'),
        expiresIn: this.configService.get<string>('jwt.refreshExpiresIn', '7d'),
      },
    );

    return {
      accessToken,
      refreshToken,
      tokenId,
    };
  }

  /**
   * Verify password using argon2
   */
  private async verifyPassword(
    hashedPassword: string,
    plainPassword: string,
  ): Promise<boolean> {
    try {
      return await argon2.verify(hashedPassword, plainPassword);
    } catch (error) {
      this.logger.error(`Password verification error: ${error.message}`);
      return false;
    }
  }
}
