import { RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { MessagesService } from '../../../modules/messages/services/messages.service';

@Injectable()
export class DeleteMessageConsumer {
  constructor(
    @Inject(forwardRef(() => MessagesService))
    private readonly messagesService: MessagesService,
  ) {}

  @RabbitSubscribe({
    exchange: 'message_exchange',
    routingKey: 'message.delete',
    queue: 'message_delete_queue',
  })
  async handleMessageDelete(payload: { messageId: number; memberId: number }) {
    await this.messagesService.hideMessageForMember(
      payload.messageId,
      payload.memberId,
    );
  }
}
