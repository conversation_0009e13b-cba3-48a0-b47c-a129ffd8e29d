import {
  BadRequestException,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  ParseIntPipe,
  Query,
  Req,
  Res,
  UnauthorizedException,
  UseInterceptors,
} from '@nestjs/common';
import { GroupMembersService } from '../services/group-member.service';
import { ImageUrlInterceptor } from '../../../common/interceptors/image-url.interceptor';
import { Response, Request } from 'express';
import { UsePermanentUrls } from '../../../common/decorators/image.decorator';

@Controller('group-members')
export class GroupMembersController {
  constructor(private readonly groupMembersService: GroupMembersService) {}

  @Get(':memberId')
  @HttpCode(HttpStatus.OK)
  @UsePermanentUrls(['imageUrl'])
  @UseInterceptors(ImageUrlInterceptor)
  async getGroupsByMemberId(
    @Param('memberId', ParseIntPipe) memberId: number,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ) {
    const user = request.user;
    if (user.id !== Number(memberId)) {
      throw new UnauthorizedException();
    }
    const result = await this.groupMembersService.getUserGroups(memberId);
    response.locals.message = 'Group member fetched successfully';
    return result;
  }

  @Get(':groupId/messages')
  @UseInterceptors(ImageUrlInterceptor)
  @UsePermanentUrls(['imageUrl'])
  async getGroupMessagesWithKey(
    @Param('groupId') groupId: string,
    @Req() req: Request,
  ) {
    (req as any).res.locals.message = 'Group details fetched successfully';

    const memberId = req.user?.id;

    const groupIdNum = Number(groupId);
    if (isNaN(groupIdNum)) {
      throw new BadRequestException('Invalid groupId');
    }

    if (!memberId) {
      throw new UnauthorizedException('Member ID missing in user payload');
    }

    return await this.groupMembersService.getGroupMessagesWithKey(
      groupIdNum,
      memberId,
    );
  }

  @Get('encryption-keys/:memberId')
  async getEncryptionKeysForMember(
    @Param('memberId', ParseIntPipe) memberId: number,
    @Req() req: Request,
  ) {
    (req as any).res.locals.message = 'Group Keys fetched successfully';

    const userId = req.user?.id;

    if (Number(userId) !== Number(memberId)) {
      throw new UnauthorizedException();
    }

    return this.groupMembersService.getAllGroupEncryptionKeysForMember(
      memberId,
    );
  }

  @Get(':groupId/members')
  @UseInterceptors(ImageUrlInterceptor)
  async getGroupMembers(@Param('groupId', ParseIntPipe) groupId: number) {
    try {
      const {
        groupId: id,
        groupName,
        groupImageUrl,
        members,
      } = await this.groupMembersService.getGroupMemberDetails(groupId);
      return { groupId: id, groupName, groupImageUrl, members };
    } catch (error) {
      if (error.message === 'Group not found') {
        throw new NotFoundException('Group not found');
      }
      throw error;
    }
  }

  @Get('sync/:memberId')
  @UsePermanentUrls(['imageUrl'])
  @UseInterceptors(ImageUrlInterceptor)
  async syncAllUserGroups(
    @Param('memberId', ParseIntPipe) memberId: number,
    @Req() req: Request,
    @Query('timestamp') lastSyncedAt: Date,
  ) {
    (req as any).res.locals.message = 'Group sync successful';

    const userId = req.user?.id;

    if (Number(userId) !== Number(memberId)) {
      throw new UnauthorizedException();
    }

    console.log('lastSyncedAt : ', lastSyncedAt);

    return this.groupMembersService.getUserGroupsSinceLastOffline(
      memberId,
      lastSyncedAt,
    );
  }

  @Get('keys/:memberId')
  async getAllGroupKeys(
    @Param('memberId', ParseIntPipe) memberId: number,
    @Req() req: Request,
    @Query('timestamp') lastSyncedAt: Date,
  ) {
    return this.groupMembersService.getUpdatedGroupEncryptionKeysForMember(
      memberId,
      lastSyncedAt,
    );
  }

  @Get('messages/sync/:memberId')
  @UsePermanentUrls(['imageUrl'])
  @UseInterceptors(ImageUrlInterceptor)
  async syncGroupMessages(
    @Param('memberId', ParseIntPipe) memberId: number,
    @Query('groupId', ParseIntPipe) groupId: number,
    @Query('timestamp') lastOffline: string,
  ) {
    if (!lastOffline) {
      throw new BadRequestException('lastOffline timestamp is required');
    }

    const lastOfflineDate = new Date(lastOffline);

    return await this.groupMembersService.syncGroupMessagesSinceLastOffline(
      groupId,
      memberId,
      lastOfflineDate,
    );
  }

  @Get('status/:memberId')
  async getMembershipStatus(
    @Query('groupId') groupId: string,
    @Param('memberId') memberId: string,
  ): Promise<{ status: 'joined' | 'left' | 'not found' }> {
    const gId = parseInt(groupId, 10);
    const mId = parseInt(memberId, 10);

    if (isNaN(gId) || isNaN(mId)) {
      throw new BadRequestException('Invalid groupId or memberId');
    }

    const status = await this.groupMembersService.getMembershipStatus(gId, mId);
    return { status };
  }
}
