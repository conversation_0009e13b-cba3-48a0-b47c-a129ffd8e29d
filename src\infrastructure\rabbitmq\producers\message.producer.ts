import { Injectable } from '@nestjs/common';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { SendMessageDto } from '../../../modules/messages/dto/send-message.dto';

@Injectable()
export class MessageProducer {
  constructor(private readonly amqpConnection: AmqpConnection) {}

  async sendMessageToQueue(payload: SendMessageDto) {
    await this.amqpConnection.publish(
      'message_exchange',
      'message.send',
      payload,
    );
  }
}
