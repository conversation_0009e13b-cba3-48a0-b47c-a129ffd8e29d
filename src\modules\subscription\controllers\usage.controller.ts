import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { UsageService } from '../services/usage.service';

@ApiTags('Usage')
@Controller('usage')
export class UsageController {
  constructor(private readonly usageService: UsageService) {}

  @Get()
  @ApiOperation({ summary: 'Get all usage records' })
  @ApiResponse({
    status: 200,
    description: 'Usage records retrieved successfully',
  })
  async findAll(@Query() query: any) {
    return this.usageService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get usage record by ID' })
  @ApiResponse({
    status: 200,
    description: 'Usage record retrieved successfully',
  })
  async findOne(@Param('id') id: string) {
    return this.usageService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create new usage record' })
  @ApiResponse({
    status: 201,
    description: 'Usage record created successfully',
  })
  async create(@Body() createUsageDto: any) {
    return this.usageService.create(createUsageDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update usage record' })
  @ApiResponse({
    status: 200,
    description: 'Usage record updated successfully',
  })
  async update(@Param('id') id: string, @Body() updateUsageDto: any) {
    return this.usageService.update(id, updateUsageDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete usage record' })
  @ApiResponse({
    status: 200,
    description: 'Usage record deleted successfully',
  })
  async remove(@Param('id') id: string) {
    return this.usageService.remove(id);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get usage records by user ID' })
  @ApiResponse({
    status: 200,
    description: 'User usage records retrieved successfully',
  })
  async findByUser(@Param('userId') userId: string, @Query() query: any) {
    return this.usageService.findByUser(userId, query);
  }

  @Get('subscription/:subscriptionId')
  @ApiOperation({ summary: 'Get usage records by subscription ID' })
  @ApiResponse({
    status: 200,
    description: 'Subscription usage records retrieved successfully',
  })
  async findBySubscription(
    @Param('subscriptionId') subscriptionId: string,
    @Query() query: any,
  ) {
    return this.usageService.findBySubscription(subscriptionId, query);
  }
}
