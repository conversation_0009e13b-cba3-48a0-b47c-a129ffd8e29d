import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Storage } from '@google-cloud/storage';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class StorageService {
  private storage: Storage;
  private bucket: string;
  private readonly logger = new Logger(StorageService.name);

  constructor(private configService: ConfigService) {
    this.storage = this.configService.get<Storage>('storage.storage')!;
    if (!this.storage) {
      throw new Error(
        'Google Cloud Storage instance is not configured properly.',
      );
    }
    this.bucket = this.configService.get(
      'storage.bucketName',
      'default-bucket-name',
    );
  }

  // Upload a single file, return relative file path
  async uploadFile(
    file: Express.Multer.File,
    folder = 'uploads',
  ): Promise<string> {
    if (!file) throw new Error('No file provided');

    try {
      const fileName = `${folder}/${uuidv4()}-${file.originalname.replace(/\s/g, '_')}`;
      const bucket = this.storage.bucket(this.bucket);
      const fileRef = bucket.file(fileName);

      const stream = fileRef.createWriteStream({
        resumable: false,
        contentType: file.mimetype,
        metadata: {
          contentType: file.mimetype,
          metadata: { originalName: file.originalname },
        },
      });

      return new Promise((resolve, reject) => {
        stream.on('error', (error) => {
          this.logger.error(
            `Error uploading file: ${error.message}`,
            error.stack,
          );
          reject(error);
        });
        stream.on('finish', () => {
          this.logger.log(`File uploaded successfully: ${fileName}`);
          resolve(fileName);
        });
        stream.end(file.buffer);
      });
    } catch (error) {
      this.logger.error(`Upload failed: ${error.message}`, error.stack);
      throw new Error(`Upload failed: ${error.message}`);
    }
  }

  // Upload multiple files, return array of relative paths
  async uploadFiles(
    files: Express.Multer.File[],
    folder = 'uploads',
  ): Promise<string[]> {
    const uploadPromises = files.map((file) => this.uploadFile(file, folder));
    return Promise.all(uploadPromises);
  }

  /**
   * Generate signed URL dynamically for given file path
   * @param filePath The relative path of the file in the bucket
   * @param expiresInSeconds Duration of the signed URL validity, can be a very high value for profile images
   * @returns Promise resolving to the signed URL string
   */
  async generateSignedUrl(
    filePath: string,
    expiresInSeconds = 600,
  ): Promise<string> {
    // Ensure expiresInSeconds is a valid positive number
    if (expiresInSeconds <= 0) {
      // Handle invalid expiry time with a default or fallback
      this.logger.warn(
        `Invalid expiry time provided: ${expiresInSeconds}. Using default.`,
      );
      expiresInSeconds = 600; // Default to 10 minutes
    }

    try {
      const fileRef = this.storage.bucket(this.bucket).file(filePath);

      // Check if file exists before generating URL
      const [exists] = await fileRef.exists();
      if (!exists) {
        this.logger.warn(`File does not exist: ${filePath}`);
        return ''; // Return empty string for non-existent files
      }

      const [url] = await fileRef.getSignedUrl({
        action: 'read',
        expires: Date.now() + expiresInSeconds * 1000,
      });

      return url;
    } catch (error) {
      this.logger.error(
        `Failed to generate signed URL for ${filePath}: ${error.message}`,
        error.stack,
      );
      // Return empty string or throw error based on how you want to handle failures
      return '';
    }
  }

  // Delete file from GCS by file path
  async deleteFileByPath(filePath: string): Promise<boolean> {
    try {
      await this.storage.bucket(this.bucket).file(filePath).delete();
      this.logger.log(`File deleted successfully: ${filePath}`);
      return true;
    } catch (error) {
      this.logger.error(`Delete failed: ${error.message}`, error.stack);
      return false;
    }
  }

  async getFileWithSignedUrl(
    filePath: string,
    expiresInSeconds = 600,
  ): Promise<{
    url: string;
    metadata: any;
  }> {
    const fileRef = this.storage.bucket(this.bucket).file(filePath);

    try {
      // Get file metadata
      const [metadata] = await fileRef.getMetadata();

      // Generate signed URL
      const [url] = await fileRef.getSignedUrl({
        action: 'read',
        expires: Date.now() + expiresInSeconds * 1000,
      });

      return {
        url,
        metadata,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get file information: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to get file information: ${error.message}`);
    }
  }

  // Add this method to batch process multiple files
  async getFilesWithSignedUrls(
    filePaths: string[],
    expiresInSeconds = 600,
  ): Promise<Array<{ path: string; url: string; metadata: any }>> {
    const promises = filePaths.map(async (path) => {
      try {
        const { url, metadata } = await this.getFileWithSignedUrl(
          path,
          expiresInSeconds,
        );
        return { path, url, metadata };
      } catch (error) {
        this.logger.warn(
          `Failed to get signed URL for ${path}: ${error.message}`,
        );
        return { path, url: '', metadata: null };
      }
    });

    return Promise.all(promises);
  }
}
