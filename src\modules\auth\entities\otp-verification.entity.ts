import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON>inColumn,
} from 'typeorm';
import { OrgMember } from '../../members/entities/org-member.entity';

@Entity('otp_verification')
export class OtpVerification {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'member_id' })
  memberId: number;

  @Column({ name: 'otp_code' })
  otpCode: string;

  @Column({ name: 'expires_at' })
  expiresAt: Date;

  @Column({ name: 'verified_at', nullable: true })
  verifiedAt: Date;

  @Column({ name: 'verification_attempts', default: 0 })
  verificationAttempts: number;

  @ManyToOne(() => OrgMember, (orgMember) => orgMember.otpVerifications)
  @JoinColumn({ name: 'member_id' })
  member: OrgMember;
}
