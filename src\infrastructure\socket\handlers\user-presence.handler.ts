import { Injectable, Logger } from '@nestjs/common';
import { Socket } from 'socket.io';
import { RedisService } from '../../redis/redis.service';
import { GroupMembersService } from '../../../modules/groups/services/group-member.service';

@Injectable()
export class UserPresenceHandler {
  private readonly logger = new Logger(UserPresenceHandler.name);

  constructor(
    private readonly redisService: RedisService,
    private readonly groupmemberservice: GroupMembersService,
  ) {}

  async handlePing(client: Socket) {
    try {
      const memberId = client.data.memberId;
      const deviceId = client.data.deviceId;

      if (memberId && deviceId) {
        await this.redisService.refreshPresence(memberId, deviceId);
      }

      // Send pong response with server timestamp
      client.emit('pong', {
        timestamp: new Date().toISOString(),
        serverTime: Date.now(),
      });
    } catch (error) {
      this.logger.error('Error handling ping:', error);
      client.emit('error', {
        code: 'PING_ERROR',
        message: 'Failed to process ping',
      });
    }
  }

  async handleGetStatus(client: Socket, data: { memberId: number }) {
    try {
      if (!data.memberId || typeof data.memberId !== 'number') {
        client.emit('error', {
          code: 'INVALID_MEMBER_ID',
          message: 'Valid member ID is required',
        });
        return;
      }

      const status = await this.redisService.getMemberStatus(data.memberId);
      client.emit('member-status', {
        status,
      });
    } catch (error) {
      this.logger.error('Error getting member status:', error);
      client.emit('error', {
        code: 'STATUS_ERROR',
        message: 'Failed to get member status',
      });
    }
  }

  async handleGetMultipleStatus(client: Socket, data: { memberIds: number[] }) {
    try {
      if (!Array.isArray(data.memberIds) || data.memberIds.length === 0) {
        client.emit('error', {
          code: 'INVALID_MEMBER_IDS',
          message: 'Valid array of member IDs is required',
        });
        return;
      }

      // Limit the number of members to prevent abuse
      const memberIds = data.memberIds.slice(0, 100);

      const statuses =
        await this.redisService.getMultipleMemberStatuses(memberIds);
      client.emit('multiple-status', statuses);
    } catch (error) {
      this.logger.error('Error getting multiple member statuses:', error);
      client.emit('error', {
        code: 'MULTIPLE_STATUS_ERROR',
        message: 'Failed to get member statuses',
      });
    }
  }

  async handleGetOnlineMembers(client: Socket) {
    try {
      const onlineMembers = await this.redisService.getOnlineMembers();
      client.emit('online-members', {
        members: onlineMembers,
        count: onlineMembers.length,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Error getting online members:', error);
      client.emit('error', {
        code: 'ONLINE_MEMBERS_ERROR',
        message: 'Failed to get online members',
      });
    }
  }

  async handleGetGroupOnlineMembers(client: Socket, data: { groupId: number }) {
    try {
      if (!data.groupId || typeof data.groupId !== 'number') {
        client.emit('error', {
          code: 'INVALID_GROUP_ID',
          message: 'Valid group ID is required',
        });
        return;
      }

      const groupMembers = await this.groupmemberservice.getGroupMembers(
        data.groupId,
      );
      if (!groupMembers || groupMembers.length === 0) {
        client.emit('group-online-members', {
          groupId: data.groupId,
          members: [],
          count: 0,
        });
        return;
      }
      const memberIds = groupMembers.map((member) => member.id);
      const statuses =
        await this.redisService.getMultipleMemberStatuses(memberIds);
      const onlineMembers = statuses.filter(
        (status) => status.status === 'online',
      );

      client.emit('group-online-members', {
        groupId: data.groupId,
        members: onlineMembers,
        count: onlineMembers.length,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Error getting group online members:', error);
      client.emit('error', {
        code: 'GROUP_ONLINE_MEMBERS_ERROR',
        message: 'Failed to get group online members',
      });
    }
  }

  async handleGetMemberDevices(client: Socket, data: { memberId: number }) {
    try {
      if (!data.memberId || typeof data.memberId !== 'number') {
        client.emit('error', {
          code: 'INVALID_MEMBER_ID',
          message: 'Valid member ID is required',
        });
        return;
      }

      const devices = await this.redisService.getMemberDevices(data.memberId);
      client.emit('member-devices', {
        memberId: data.memberId,
        ...devices,
      });
    } catch (error) {
      this.logger.error('Error getting member devices:', error);
      client.emit('error', {
        code: 'DEVICES_ERROR',
        message: 'Failed to get member devices',
      });
    }
  }

  async handleForceRefreshStatus(client: Socket) {
    try {
      const memberId = client.data.memberId;
      const deviceId = client.data.deviceId;

      if (!memberId || !deviceId) {
        client.emit('error', {
          code: 'INVALID_CLIENT_DATA',
          message: 'Client data is incomplete',
        });
        return;
      }

      // Force refresh presence
      await this.redisService.refreshPresence(memberId, deviceId);

      // Get updated status
      const status = await this.redisService.getMemberStatus(memberId);
      client.emit('status-refreshed', { status });
    } catch (error) {
      this.logger.error('Error force refreshing status:', error);
      client.emit('error', {
        code: 'REFRESH_ERROR',
        message: 'Failed to refresh status',
      });
    }
  }
}
