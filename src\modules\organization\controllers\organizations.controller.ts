import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
  Put,
  Res,
  Req,
  UnauthorizedException,
  UseInterceptors,
} from '@nestjs/common';
import { OrganizationsService } from '../services/organizations.service';
import { CreateOrganizationDto, UpdateOrganizationDto } from '../dto';
import { Organization } from '../entities/organization.entity';
import { Response, Request } from 'express';
import { Public } from '../../../common/decorators/public.decorator';
import { ImageUrlInterceptor } from '../../../common/interceptors/image-url.interceptor';

@Controller('organizations')
export class OrganizationsController {
  constructor(private readonly organizationsService: OrganizationsService) {}

  @Post('create-org')
  @HttpCode(HttpStatus.OK)
  create(
    @Body() createOrganizationDto: CreateOrganizationDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<Organization> {
    const user = request.user;
    if (user.roleId !== 1) {
      throw new UnauthorizedException();
    }

    response.locals.message = 'Organization created successfully';
    return this.organizationsService.create(createOrganizationDto);
  }

  @Public()
  @Get(':id')
  @UseInterceptors(ImageUrlInterceptor)
  findOne(
    @Param('id', ParseIntPipe) id: number,
    @Res({ passthrough: true }) response: Response,
  ): Promise<Organization> {
    response.locals.message = 'Organization fetched successfully';
    return this.organizationsService.findOne(id);
  }

  @Get()
  @UseInterceptors(ImageUrlInterceptor)
  findAll(
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ) {
    const user = request.user;
    if (user.roleId !== 1) {
      throw new UnauthorizedException();
    }
    response.locals.message = 'Organizations fetched successfully';
    return this.organizationsService.findAll();
  }

  @Put(':id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<Organization> {
    const user = request.user;

    if (user.roleId !== 1) {
      throw new UnauthorizedException();
    }

    response.locals.message = 'Organization updated successfully';
    return this.organizationsService.update(id, updateOrganizationDto);
  }
}
