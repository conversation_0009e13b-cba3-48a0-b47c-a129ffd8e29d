import { registerAs } from '@nestjs/config';
import { Storage } from '@google-cloud/storage';
import * as path from 'path';

export const storageConfig = registerAs('storage', () => {
  const keyFilePath = process.env.GCS_KEY_FILE_NAME;
  const googleClientId = process.env.GOOGLE_CLIENT_ID;
  const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET;
  const projectId = process.env.GCS_PROJECT;
  const bucketName = process.env.GCS_BUCKET_NAME;

  // Validate required environment variables
  if (!keyFilePath) {
    throw new Error('GCS_KEY_FILE_NAME not defined in .env');
  }

  if (!googleClientId || !googleClientSecret) {
    throw new Error('Google client credentials not defined in .env');
  }

  if (!projectId) {
    throw new Error('GCS_PROJECT not defined in .env');
  }

  if (!bucketName) {
    throw new Error('GCS_BUCKET_NAME not defined in .env');
  }

  // Initialize Google Cloud Storage
  const storage = new Storage({
    keyFilename: path.resolve(keyFilePath),
    projectId,
  });

  // Return the configuration object
  return {
    storage,
    googleClientId,
    googleClientSecret,
    projectId,
    bucketName,
    keyFilename: path.resolve(keyFilePath),
    defaultFolder: process.env.STORAGE_DEFAULT_FOLDER || 'uploads',
    imageUrlExpiry: parseInt(
      process.env.STORAGE_IMAGE_URL_EXPIRY || '3600',
      10,
    ),
    permanentUrlExpiry: parseInt(
      process.env.STORAGE_PERMANENT_URL_EXPIRY || '315360000',
      10,
    ),
  };
});
