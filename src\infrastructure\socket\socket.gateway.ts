// socket.gateway.ts
import {
  WebSocketGateway,
  OnGatewayConnection,
  OnGatewayDisconnect,
  SubscribeMessage,
  WebSocketServer,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import {
  UseInterceptors,
  Logger,
  forwardRef,
  Inject,
  UseGuards,
} from '@nestjs/common';
import { WsAuthInterceptor } from './interceptors/ws-auth.interceptor';
import { RedisService } from '../redis/redis.service';
import { UserPresenceHandler } from './handlers/user-presence.handler';
import { SystemHandler } from './handlers/system.handler';
import { MessageHandler } from './handlers/message.handler';
import { SendMessageDto } from '../../modules/messages/dto/send-message.dto';
import { MessagesService } from '../../modules/messages/services/messages.service';
import { MembersService } from '../../modules/members/services/members.service';
import { MessageReadDto } from '../../modules/messages/dto/message-read.dto';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';

@WebSocketGateway({
  cors: { origin: '*' },
})
@UseInterceptors(WsAuthInterceptor)
export class SocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer() server: Server;
  private readonly logger = new Logger(SocketGateway.name);

  constructor(
    private readonly redisService: RedisService,
    @Inject(forwardRef(() => MessagesService))
    private readonly messagesService: MessagesService,
    @Inject(forwardRef(() => MembersService))
    private readonly membersService: MembersService,
    private readonly userPresenceHandler: UserPresenceHandler,
    private readonly systemHandler: SystemHandler,
    private readonly messageHandler: MessageHandler,
    private readonly amqpConnection: AmqpConnection,
  ) {}

  afterInit(server: Server) {
    this.server = server;
    this.setupPresenceListener();
  }

  private setupPresenceListener() {
    this.redisService.subscribeToPresenceChanges((message) => {
      try {
        const presenceData = JSON.parse(message);
        const formattedPresenceData = {
          ...presenceData,
          memberId: presenceData.memberId.toString(),
        };

        // Broadcast to all connected clients
        this.server.emit('user-status', formattedPresenceData);

        this.server
          .to(`member_${presenceData.memberId}`)
          .emit('status-updated', presenceData);
      } catch (error) {
        this.logger.error('Error processing presence change:', error);
      }
    });
  }

  async handleConnection(client: Socket) {
    try {
      const memberId = client.handshake?.auth?.memberId;

      const deviceId =
        client.handshake.auth?.deviceId ||
        client.handshake.headers['x-device-id'];

      if (!deviceId) {
        this.logger.warn(`Missing deviceId for memberId ${memberId}`);
        client.emit('error', {
          code: 'MISSING_DEVICE_ID',
          message: 'Device ID is required for presence tracking',
        });
        client.disconnect();
        return;
      }

      // Validate deviceId format (basic validation)
      if (typeof deviceId !== 'string' || deviceId.length < 10) {
        this.logger.warn(
          `Invalid deviceId format for memberId ${memberId}: ${deviceId}`,
        );
        client.emit('error', {
          code: 'INVALID_DEVICE_ID',
          message: 'Invalid device ID format',
        });
        client.disconnect();
        return;
      }

      client.data.deviceId = deviceId;
      client.data.memberId = memberId;

      // Join member-specific room for targeted updates
      client.join(`member_${memberId}`);

      this.logger.log(
        `Client connected — memberId: ${memberId}, deviceId: ${deviceId}, socketId: ${client.id}`,
      );

      // Set member online in Redis
      await this.redisService.setMemberOnline(memberId, deviceId);

      // Send current status to the connected client
      const currentStatus = await this.redisService.getMemberStatus(memberId);
      client.emit('your-status', {
        ...currentStatus,
      });

      // Send connection confirmation
      client.emit('connected', {
        memberId,
        deviceId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Error handling connection:', error);
      client.emit('error', {
        code: 'CONNECTION_ERROR',
        message: 'Failed to establish connection',
      });
      client.disconnect();
    }
  }

  async handleDisconnect(client: Socket) {
    try {
      const memberId = client.handshake?.auth?.memberId;

      const deviceId =
        client.handshake.auth?.deviceId ||
        client.handshake.headers['x-device-id'];

      this.logger.log(
        `Client disconnected — memberId: ${memberId}, deviceId: ${deviceId}, socketId: ${client.id}`,
      );

      if (memberId && deviceId) {
        await this.redisService.setMemberOffline(memberId, deviceId);
      }
    } catch (error) {
      this.logger.error('Error handling disconnect:', error);
    }
  }

  // User Presence related handlers - delegated to UserPresenceHandler
  @SubscribeMessage('ping')
  async handlePing(client: Socket) {
    return this.userPresenceHandler.handlePing(client);
  }

  @SubscribeMessage('get-status')
  async handleGetStatus(client: Socket, data: { memberId: number }) {
    return this.userPresenceHandler.handleGetStatus(client, data);
  }

  @SubscribeMessage('get-multiple-status')
  async handleGetMultipleStatus(client: Socket, data: { memberIds: number[] }) {
    return this.userPresenceHandler.handleGetMultipleStatus(client, data);
  }

  @SubscribeMessage('get-online-members')
  async handleGetOnlineMembers(client: Socket) {
    return this.userPresenceHandler.handleGetOnlineMembers(client);
  }

  @SubscribeMessage('get-member-devices')
  async handleGetMemberDevices(client: Socket, data: { memberId: number }) {
    return this.userPresenceHandler.handleGetMemberDevices(client, data);
  }

  @SubscribeMessage('send_message')
  async handleSendMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() dto: SendMessageDto,
  ) {
    try {
      await this.messageHandler.handleSendMessage(dto);
    } catch (err) {
      client.emit('error', {
        message: 'Failed to send message',
        error: err.message,
      });
    }
  }

  @SubscribeMessage('user_typing')
  async handleUserTyping(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { groupId: number; isTyping: boolean },
  ) {
    const memberId = client.data.memberId;

    const member = await this.membersService.findOne(memberId);

    if (!member) {
      this.logger.warn(`Member not found: ${memberId}`);
      return;
    }

    this.server.to(String(data.groupId)).emit('user_typing', {
      groupId: data.groupId,
      memberId: client.data.memberId,
      memberName: member.name,
      isTyping: data.isTyping,
    });
  }

  @SubscribeMessage('message_read')
  async handleMessageRead(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { groupId: number; readerId: number },
  ) {
    try {
      const readRecords = await this.messagesService.markMessageAsRead(
        data.groupId,
        data.readerId,
      );

      if (!readRecords || readRecords.length === 0) {
        return;
      }

      const groupId = data.groupId;

      // Prepare array of read message IDs with their readAt timestamp
      const messages = readRecords.map((record) => ({
        messageId: record.messageId,
        readAt: record.readAt,
      }));

      // Emit to all members in the group
      const payload: MessageReadDto = {
        groupId: data.groupId,
        readerId: data.readerId,
        messages,
      };

      await this.amqpConnection.publish(
        'message_exchange',
        'message.read',
        payload,
      );
    } catch (error) {
      client.emit('error', {
        code: 'MESSAGE_READ_FAILED',
        message: error.message,
      });
    }
  }

  emitGroupEvent(groupId: number, event: string, payload: any) {
    this.server.to(String(groupId)).emit(event, payload);
  }

  @SubscribeMessage('join_room')
  handleJoinRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { groupId: number },
  ) {
    client.join(String(data.groupId));
    this.logger.log(`Client ${client.id} joined room ${data.groupId}`);
  }

  @SubscribeMessage('leave_room')
  handleLeaveRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { groupId: number },
  ) {
    client.leave(String(data.groupId));
    this.logger.log(`Client ${client.id} left room ${data.groupId}`);
  }

  @SubscribeMessage('force-refresh-status')
  async handleForceRefreshStatus(client: Socket) {
    return this.userPresenceHandler.handleForceRefreshStatus(client);
  }

  @SubscribeMessage('health-check')
  async handleHealthCheck(client: Socket) {
    return this.systemHandler.handleHealthCheck(client, this.server);
  }

  @SubscribeMessage('debug-presence')
  async handleDebugPresence(client: Socket, data: { memberId: number }) {
    return this.systemHandler.handleDebugPresence(client, data);
  }

  @SubscribeMessage('delete_message_for_me')
  async handleDeleteMessageForMe(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { messageId: number },
  ) {
    try {
      const memberId = client.data.memberId;
      if (!memberId) throw new Error('Member ID missing from client');
      console.log('Deleting message for member:', memberId, data);

      await this.messagesService.deleteMessageForMember(
        data.messageId,
        memberId,
      );

      // Optionally notify just this client
      client.emit('message_deleted_for_me', {
        messageId: data.messageId,
      });
    } catch (error) {
      this.logger.error('Error deleting message for member', error);
      client.emit('error', {
        code: 'DELETE_MESSAGE_FAILED',
        message: error.message,
      });
    }
  }
  @SubscribeMessage('delete_messages_everyone')
  async handleDeleteMessagesForEveryone(
    @MessageBody() data: { messageIds: number[]; memberId: number },
    @ConnectedSocket() client: Socket,
  ) {
    try {
      const deletedIds = await this.messagesService.deleteMessagesForEveryone(
        data.messageIds,
        data.memberId,
      );

      // Optionally notify all clients in the group
      this.server.emit('messages_deleted_for_everyone', {
        messageIds: deletedIds,
      });
    } catch (error) {
      client.emit('error', {
        type: 'delete_failed',
        message: error.message,
      });
    }
  }

  broadcastToMember(memberId: number, event: string, data: any) {
    this.server.to(`member_${memberId}`).emit(event, data);
  }

  async getConnectedClientsCount(): Promise<number> {
    const sockets = await this.server.fetchSockets();
    return sockets.length;
  }
}
