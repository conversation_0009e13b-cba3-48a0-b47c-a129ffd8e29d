import {
  Controller,
  Get,
  Param,
  Query,
  Res,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { Response } from 'express';
import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard';
import { MediaDownloadService } from '../services/media-download.service';

/**
 * Media Download Controller
 *
 * Handles HTTP requests for media file downloads including
 * direct downloads, thumbnail downloads, and secure file access.
 */
@Controller('media/download')
@UseGuards(JwtAuthGuard)
export class MediaDownloadController {
  constructor(private readonly mediaDownloadService: MediaDownloadService) {}

  /**
   * Download media file
   */
  @Get(':fileId')
  async downloadMediaFile(
    @Param('fileId') fileId: string,
    @Query('userId', ParseIntPipe) userId: number,
    @Res() res: Response,
  ) {
    // TODO: Implement media file download
    const fileStream = await this.mediaDownloadService.downloadFile(
      fileId,
      userId,
    );
    return fileStream.pipe(res);
  }

  /**
   * Download thumbnail
   */
  @Get(':fileId/thumbnail')
  async downloadThumbnail(
    @Param('fileId') fileId: string,
    @Query('size') size: string = 'medium',
    @Res() res: Response,
  ) {
    // TODO: Implement thumbnail download
    const thumbnailStream = await this.mediaDownloadService.downloadThumbnail(
      fileId,
      size,
    );
    return thumbnailStream.pipe(res);
  }

  /**
   * Download compressed version
   */
  @Get(':fileId/compressed')
  async downloadCompressed(
    @Param('fileId') fileId: string,
    @Query('quality') quality: string = 'medium',
    @Res() res: Response,
  ) {
    // TODO: Implement compressed file download
    const compressedStream = await this.mediaDownloadService.downloadCompressed(
      fileId,
      quality,
    );
    return compressedStream.pipe(res);
  }

  /**
   * Generate secure download URL
   */
  @Get(':fileId/url')
  async generateDownloadUrl(
    @Param('fileId') fileId: string,
    @Query('userId', ParseIntPipe) userId: number,
    @Query('expiresIn', ParseIntPipe) expiresIn: number = 3600,
  ) {
    // TODO: Implement secure URL generation
    const url = await this.mediaDownloadService.generateSecureUrl(
      fileId,
      userId,
      expiresIn,
    );
    return { downloadUrl: url, expiresIn };
  }

  /**
   * Download with range support (for video streaming)
   */
  @Get(':fileId/range')
  async downloadWithRange(
    @Param('fileId') fileId: string,
    @Query('start', ParseIntPipe) start: number,
    @Query('end', ParseIntPipe) end: number,
    @Res() res: Response,
  ) {
    // TODO: Implement range download
    const rangeStream = await this.mediaDownloadService.downloadRange(
      fileId,
      start,
      end,
    );
    return rangeStream.pipe(res);
  }
}
