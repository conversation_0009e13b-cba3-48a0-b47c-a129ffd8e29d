import { BadRequestException } from '@nestjs/common';
import { Request } from 'express';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';

/**
 * Default function to generate filenames for uploaded files
 */
export const generateFilename = (
  req: Request,
  file: Express.Multer.File,
  callback: (error: Error | null, filename: string) => void,
) => {
  // Create a unique file name to avoid collisions
  const uniqueSuffix = `${Date.now()}-${uuidv4()}`;
  // Replace spaces with underscores and remove other problematic characters
  const originalName = file.originalname
    .replace(/\s/g, '_')
    .replace(/[^a-zA-Z0-9_.-]/g, '');
  // Generate the final filename
  const filename = `${path.parse(originalName).name}-${uniqueSuffix}${path.parse(originalName).ext}`;
  callback(null, filename);
};

/**
 * Filter for image files only
 */
export const imageFileFilter = (
  req: Request,
  file: Express.Multer.File,
  callback: (error: Error | null, acceptFile: boolean) => void,
) => {
  // Accept image files only
  if (!file.mimetype.match(/^image\/(jpeg|png|gif|webp|svg\+xml)$/)) {
    return callback(
      new BadRequestException(
        'Only image files (JPEG, PNG, GIF, WEBP, SVG) are allowed',
      ),
      false,
    );
  }
  callback(null, true);
};

/**
 * Filter for common document files
 */
export const documentFileFilter = (
  req: Request,
  file: Express.Multer.File,
  callback: (error: Error | null, acceptFile: boolean) => void,
) => {
  // Accept documents (PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT)
  if (
    !file.mimetype.match(
      /^application\/(pdf|msword|vnd\.openxmlformats-officedocument\.wordprocessingml\.document|vnd\.ms-excel|vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet|vnd\.ms-powerpoint|vnd\.openxmlformats-officedocument\.presentationml\.presentation)$|^text\/plain$/,
    )
  ) {
    return callback(
      new BadRequestException(
        'Only document files (PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT) are allowed',
      ),
      false,
    );
  }
  callback(null, true);
};

/**
 * Generate a folder name based on the request and file context
 * You can customize this to create dynamic folder structure
 */
export const generateFolderPath = (
  req: Request,
  file: Express.Multer.File,
  entityType: string,
  entityId?: string | number,
): string => {
  // Get current date for folder organization
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');

  // Base path on entity type (users, organizations, messages, etc.)
  let basePath = `${entityType}`;

  // If entity ID provided, add to path
  if (entityId) {
    basePath += `/${entityId}`;
  }

  // Create dated folder structure
  return `${basePath}/${year}/${month}`;
};

/**
 * Default file size limits
 */
export const defaultLimits = {
  // 5 MB for images
  imageSize: 5 * 1024 * 1024,
  // 20 MB for documents
  documentSize: 20 * 1024 * 1024,
  // 50 MB for videos
  videoSize: 50 * 1024 * 1024,
  // 10 MB for audio
  audioSize: 10 * 1024 * 1024,
};
