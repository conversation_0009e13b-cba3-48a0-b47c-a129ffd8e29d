// src/modules/messages/dto/message-read.dto.ts
import {
  IsArray,
  IsDate,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsPositive,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class ReadMessageDto {
  @IsInt()
  @IsPositive()
  messageId: number;

  @IsDate()
  @Type(() => Date)
  readAt: Date;
}

export class MessageReadDto {
  @IsInt()
  @IsPositive()
  groupId: number;

  @IsInt()
  @IsPositive()
  readerId: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReadMessageDto)
  messages: ReadMessageDto[];
}
