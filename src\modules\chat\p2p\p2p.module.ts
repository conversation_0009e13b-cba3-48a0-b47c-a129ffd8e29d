import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { KeyExchangeService } from './services/key-exchange.service';
import { MessageEncryptionService } from './services/message-encryption.service';
import { SessionEstablishmentService } from './services/session-establishment.service';
import { SignalMessagingService } from './services/signal-messaging.service';
import { SessionInitController } from './controllers/session-init.controller';
import { SignalChatController } from './controllers/signal-chat.controller';

/**
 * P2P Module
 *
 * Handles peer-to-peer messaging functionality including
 * end-to-end encryption, key exchange, session establishment,
 * and secure messaging using Signal Protocol.
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Add P2P-related entities here when created
    ]),
  ],
  controllers: [SessionInitController, SignalChatController],
  providers: [
    KeyExchangeService,
    MessageEncryptionService,
    SessionEstablishmentService,
    SignalMessagingService,
  ],
  exports: [
    KeyExchangeService,
    MessageEncryptionService,
    SessionEstablishmentService,
    SignalMessagingService,
  ],
})
export class P2pModule {}
