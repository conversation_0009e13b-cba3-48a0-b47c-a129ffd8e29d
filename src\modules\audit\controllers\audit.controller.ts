import {
  Controller,
  Get,
  Post,
  Query,
  Body,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuditService } from '../services/audit.service';
import { LogAggregationService } from '../services/log-aggregation.service';

/**
 * Audit Controller
 *
 * Handles HTTP requests related to audit logging,
 * log retrieval, and audit trail management.
 */
@ApiTags('audit')
@Controller('audit')
// @UseGuards(JwtAuthGuard, RolesGuard)
export class AuditController {
  private readonly logger = new Logger(AuditController.name);

  constructor(
    private readonly auditService: AuditService,
    private readonly logAggregationService: LogAggregationService,
  ) {}

  /**
   * Get audit logs with optional filtering
   */
  @Get('logs')
  @ApiOperation({ summary: 'Retrieve audit logs' })
  @ApiResponse({
    status: 200,
    description: 'Audit logs retrieved successfully',
  })
  async getAuditLogs(@Query() filters: any) {
    this.logger.log('Getting audit logs');
    return this.auditService.getAuditLogs(filters);
  }

  /**
   * Generate audit report
   */
  @Post('reports/generate')
  @ApiOperation({ summary: 'Generate audit report' })
  @ApiResponse({
    status: 201,
    description: 'Audit report generated successfully',
  })
  async generateAuditReport(@Body() reportParams: any) {
    this.logger.log('Generating audit report');
    const { startDate, endDate } = reportParams;
    return this.auditService.generateAuditReport(
      new Date(startDate),
      new Date(endDate),
    );
  }

  /**
   * Search logs
   */
  @Get('logs/search')
  @ApiOperation({ summary: 'Search audit logs' })
  @ApiResponse({
    status: 200,
    description: 'Log search completed successfully',
  })
  async searchLogs(
    @Query('query') query: string,
    @Query('limit') limit?: number,
  ) {
    this.logger.log(`Searching logs with query: ${query}`);
    return this.logAggregationService.searchLogs(query, {}, limit);
  }

  /**
   * Get log statistics
   */
  @Get('statistics')
  @ApiOperation({ summary: 'Get audit log statistics' })
  @ApiResponse({
    status: 200,
    description: 'Statistics retrieved successfully',
  })
  async getLogStatistics(
    @Query('organizationId') organizationId?: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    this.logger.log('Getting log statistics');
    return this.logAggregationService.generateLogStatistics(
      organizationId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }
}
