@echo off
REM Read version from version.txt
set /p VERSION=<version.txt

REM Docker Hub username
set USERNAME=yathupiraba

REM Build backend image
docker build -t %USERNAME%/chat-backend:version%VERSION% .

REM Push backend image
docker push %USERNAME%/chat-backend:version%VERSION%

REM Increment version
set /a NEXT_VERSION=%VERSION%+1
echo %NEXT_VERSION% > version.txt

echo Backend image pushed: version %VERSION%
pause
