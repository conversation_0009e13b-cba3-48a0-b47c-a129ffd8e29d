import {
  Controller,
  Post,
  Body,
  Req,
  Res,
  Get,
  HttpCode,
  HttpStatus,
  UseInterceptors,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { AuthService } from '../services/auth.service';
import { AdminLoginDto, AdminResponseDto, RefreshTokenDto } from '../dto';
import { Public } from '../../../common/decorators/public.decorator';
import { RefreshTokenPayload } from '../interfaces/refresh-token-payload.interface';
import { ConfigService } from '@nestjs/config';
import { ImageUrlInterceptor } from '../../../common/interceptors/image-url.interceptor';
import { UsePermanentUrls } from '../../../common/decorators/image.decorator';
import { OtpService } from '../services/otp.service';
import { MobileLogoutDto } from '../dto/mobile-logout.dto';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly otpService: OtpService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Login endpoint for both ProductAdmin and OrgAdmin
   */
  @Public()
  @Post('login')
  @HttpCode(HttpStatus.OK)
  async login(
    @Body() loginDto: AdminLoginDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const result = await this.authService.login(loginDto);

    // Set refresh token in HTTP-only cookie
    this.setRefreshTokenCookie(response, result.refreshToken);
    // Set access token in HTTP-only cookie
    this.setAccessTokenCookie(response, result.accessToken);

    response.locals.message = 'Login successfully';

    const { refreshToken, accessToken, ...responseData } = result;

    // const { refreshToken, ...responseData } = result;
    return responseData;
  }

  @Get('user-details')
  @UseInterceptors(ImageUrlInterceptor)
  @UsePermanentUrls(['imageUrl'])
  async getMe(@Req() req: Request): Promise<AdminResponseDto> {
    (req as any).res.locals.message = 'User details fetched successfully';
    return this.authService.getUserDetails(req.user);
  }

  /**
   * Refresh token endpoint
   */
  @Public()
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  async refresh(
    @Body() refreshTokenDto: RefreshTokenDto,
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response,
  ) {
    // Get refresh token from cookie or request body
    const refreshToken =
      request.cookies?.refresh_token || refreshTokenDto.refreshToken;

    if (!refreshToken) {
      return {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Refresh token is required',
      };
    }

    const tokens = await this.authService.refreshToken(refreshToken);

    // Set new refresh token in cookie
    this.setRefreshTokenCookie(response, tokens.refreshToken);
    this.setAccessTokenCookie(response, tokens.accessToken);

    // Only return access token in response
    return {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
    };
  }

  /**
   * Logout endpoint
   */
  @Public()
  @Post('logout')
  @HttpCode(HttpStatus.OK)
  async logout(
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response,
  ) {
    const user = request.user;

    const refreshToken = request.cookies?.refresh_token;

    if (refreshToken) {
      try {
        const decoded = (await this.authService.decodeRefreshToken(
          refreshToken,
        )) as RefreshTokenPayload;
        await this.authService.logout(user.userId, user.type, decoded.tokenId);
      } catch (error) {}
    }

    // Clear refresh token cookie
    this.clearRefreshTokenCookie(response);
    this.clearAccessTokenCookie(response);

    return {
      message: 'Logged out successfully',
    };
  }

  /**
   * Logout from all devices
   */
  @Public()
  @Post('logout-all')
  @HttpCode(HttpStatus.OK)
  async logoutAll(
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response,
  ) {
    const user = request.user as any;

    // Remove all refresh tokens for this user
    await this.authService.logoutAll(user.userId, user.type);

    // Clear refresh token cookie
    this.clearRefreshTokenCookie(response);

    return {
      message: 'Logged out from all devices successfully',
    };
  }

  @Public()
  @Post('mobile/logout')
  @HttpCode(HttpStatus.OK)
  async mobileLogout(@Body() body: MobileLogoutDto) {
    const { refreshToken, memberId, deviceId } = body;

    if (!refreshToken) {
      throw new BadRequestException('Refresh token is required');
    }

    try {
      // Decode the token to get user and tokenId
      const decoded = (await this.authService.decodeRefreshToken(
        refreshToken,
      )) as RefreshTokenPayload;

      const tokenId = decoded.tokenId;
      const userId = decoded.sub;
      let userType: 'admin' | 'org_member';
      if (decoded.type === 'product_admin' || decoded.type === 'org_admin') {
        userType = 'admin';
      } else if (decoded.type === 'org_member') {
        userType = 'org_member';
      } else {
        throw new UnauthorizedException('Invalid token type');
      }

      if (!userId || !userType) {
        throw new UnauthorizedException('Invalid token payload structure');
      }
      await this.otpService.logout(memberId, deviceId);
      await this.authService.logout(userId, userType, tokenId);

      return { message: 'Logged out successfully' };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  /**
   * Helper methods for cookie management
   */

  /**
   * Set refresh token as HTTP-only cookie
   */
  setRefreshTokenCookie(response: Response, token: string): void {
    const refreshExpiresInDays = parseInt(
      this.configService
        .get<string>('jwt.refreshExpiresIn', '7d')
        .replace('d', '') || '7',
    );
    const sameSite = this.configService.get<'lax' | 'strict' | 'none'>(
      'COOKIE_SAMESITE',
      'lax',
    );

    response.cookie('refresh_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV?.trim() === 'production',
      sameSite: sameSite,
      maxAge: refreshExpiresInDays * 24 * 60 * 60 * 1000,
    });
  }

  setAccessTokenCookie(response: Response, token: string) {
    const accessExpiresInDays = parseInt(
      this.configService.get<string>('jwt.expiresIn', '1d').replace('d', '') ||
        '1',
    );
    const sameSite = this.configService.get<'lax' | 'strict' | 'none'>(
      'COOKIE_SAMESITE',
      'lax',
    );
    response.cookie('access_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV?.trim() === 'production',
      sameSite: sameSite,
      maxAge: accessExpiresInDays * 24 * 60 * 60 * 1000,
    });
  }

  /**
   * Clear refresh token cookie
   */
  private clearRefreshTokenCookie(response: Response): void {
    response.clearCookie('refresh_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV?.trim() === 'production',
    });
  }

  private clearAccessTokenCookie(response: Response): void {
    response.clearCookie('access_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV?.trim() === 'production',
    });
  }
}
