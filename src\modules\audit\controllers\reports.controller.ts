import {
  Controller,
  Get,
  Post,
  Query,
  Body,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { RetentionService } from '../services/retention.service';

/**
 * Reports Controller
 *
 * Handles HTTP requests related to data retention,
 * cleanup policies, and retention reporting.
 */
@ApiTags('reports')
@Controller('reports')
// @UseGuards(JwtAuthGuard, RolesGuard)
export class ReportsController {
  private readonly logger = new Logger(ReportsController.name);

  constructor(private readonly retentionService: RetentionService) {}

  /**
   * Get retention policy for organization
   */
  @Get('retention-policy')
  @ApiOperation({ summary: 'Get retention policy' })
  @ApiResponse({
    status: 200,
    description: 'Retention policy retrieved successfully',
  })
  async getRetentionPolicy(@Query('organizationId') organizationId: number) {
    this.logger.log(`Getting retention policy for org ${organizationId}`);
    return this.retentionService.getRetentionPolicy(organizationId);
  }

  /**
   * Update retention policy for organization
   */
  @Post('retention-policy')
  @ApiOperation({ summary: 'Update retention policy' })
  @ApiResponse({
    status: 201,
    description: 'Retention policy updated successfully',
  })
  async updateRetentionPolicy(@Body() policyData: any) {
    this.logger.log('Updating retention policy');
    const { organizationId, policy } = policyData;
    return this.retentionService.updateRetentionPolicy(organizationId, policy);
  }

  /**
   * Execute manual retention cleanup
   */
  @Post('retention/cleanup')
  @ApiOperation({ summary: 'Execute retention cleanup' })
  @ApiResponse({
    status: 201,
    description: 'Retention cleanup executed successfully',
  })
  async executeRetentionCleanup(@Body() cleanupParams: any) {
    this.logger.log('Executing manual retention cleanup');
    const { organizationId, type, retentionPeriodDays } = cleanupParams;

    switch (type) {
      case 'audit':
        return this.retentionService.applyAuditLogRetention(
          organizationId,
          retentionPeriodDays,
        );
      case 'messages':
        return this.retentionService.applyMessageRetention(
          organizationId,
          retentionPeriodDays,
        );
      case 'media':
        return this.retentionService.applyMediaRetention(
          organizationId,
          retentionPeriodDays,
        );
      default:
        throw new Error('Invalid cleanup type');
    }
  }

  /**
   * Execute scheduled cleanup
   */
  @Post('retention/scheduled-cleanup')
  @ApiOperation({ summary: 'Execute scheduled retention cleanup' })
  @ApiResponse({
    status: 201,
    description: 'Scheduled cleanup executed successfully',
  })
  async executeScheduledCleanup() {
    this.logger.log('Executing scheduled retention cleanup');
    return this.retentionService.executeScheduledCleanup();
  }
}
