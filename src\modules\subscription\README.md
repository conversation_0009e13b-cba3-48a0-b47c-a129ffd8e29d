# Subscription Module - Basic Layout

## 📁 Directory Structure

```
src/modules/subscription/
├── controllers/
│   ├── subscription.controller.ts    # Main subscription management
│   ├── usage.controller.ts          # Usage tracking and monitoring
│   ├── plan.controller.ts           # Subscription plans management
│   ├── billing.controller.ts        # Billing and payment handling
│   └── index.ts                     # Export all controllers
├── services/
│   ├── subscription.service.ts      # Core subscription logic
│   ├── usage.service.ts            # Usage tracking service
│   ├── plan.service.ts             # Plan management service
│   ├── billing.service.ts          # Billing and payment service
│   └── index.ts                    # Export all services
├── subscription.module.ts          # Module definition
└── README.md                       # This file
```

## 🎯 Controllers Overview

### SubscriptionController (`/subscriptions`)
- `GET /` - Get all subscriptions
- `GET /:id` - Get subscription by ID
- `POST /` - Create new subscription
- `PUT /:id` - Update subscription
- `DELETE /:id` - Delete subscription
- `POST /:id/activate` - Activate subscription
- `POST /:id/deactivate` - Deactivate subscription
- `POST /:id/cancel` - Cancel subscription

### UsageController (`/usage`)
- `GET /` - Get all usage records
- `GET /:id` - Get usage record by ID
- `POST /` - Create new usage record
- `PUT /:id` - Update usage record
- `DELETE /:id` - Delete usage record
- `GET /user/:userId` - Get usage by user
- `GET /subscription/:subscriptionId` - Get usage by subscription

### PlanController (`/plans`)
- `GET /` - Get all subscription plans
- `GET /:id` - Get plan by ID
- `POST /` - Create new plan
- `PUT /:id` - Update plan
- `DELETE /:id` - Delete plan
- `GET /active` - Get active plans
- `POST /:id/activate` - Activate plan
- `POST /:id/deactivate` - Deactivate plan

### BillingController (`/billing`)
- `GET /` - Get all billing records
- `GET /:id` - Get billing record by ID
- `POST /` - Create new billing record
- `PUT /:id` - Update billing record
- `DELETE /:id` - Delete billing record
- `GET /user/:userId` - Get billing by user
- `POST /:id/pay` - Process payment
- `GET /:id/invoice` - Generate invoice

## 🔧 Services Overview

### SubscriptionService
- Core subscription management logic
- Handles subscription lifecycle (create, update, activate, deactivate, cancel)
- User subscription management
- Active subscription queries

### UsageService
- Usage tracking and monitoring
- User usage statistics
- Subscription usage analytics
- Usage data management

### PlanService
- Subscription plan management
- Plan activation/deactivation
- Plan filtering and queries
- Plan type and pricing management

### BillingService
- Billing record management
- Payment processing
- Invoice generation
- Billing analytics and reporting

## 🚀 Module Configuration

The `SubscriptionModule` is configured with:

```typescript
@Module({
  controllers: [
    SubscriptionController,
    BillingController,
    PlanController,
    UsageController,
  ],
  providers: [
    SubscriptionService,
    BillingService,
    UsageService,
    PlanService,
  ],
  exports: [],
})
export class SubscriptionModule {}
```

## 📋 API Routes

All routes are prefixed with `/api/v1/` (from your backend configuration):

- **Subscriptions**: `/api/v1/subscriptions/*`
- **Usage**: `/api/v1/usage/*`
- **Plans**: `/api/v1/plans/*`
- **Billing**: `/api/v1/billing/*`

## 🔗 Integration

To use this module in your main application:

```typescript
// In app.module.ts
import { SubscriptionModule } from './modules/subscription/subscription.module';

@Module({
  imports: [
    // ... other modules
    SubscriptionModule,
  ],
  // ...
})
export class AppModule {}
```

## 📝 Next Steps

1. **Add DTOs** - Create data transfer objects for request/response validation
2. **Add Entities** - Define database entities for TypeORM/Mongoose
3. **Add Guards** - Implement authentication and authorization
4. **Add Interceptors** - Add logging, caching, and transformation
5. **Add Tests** - Unit and integration tests for controllers and services
6. **Add Documentation** - Swagger/OpenAPI documentation
7. **Add Validation** - Request validation with class-validator

This basic layout provides a solid foundation for building a comprehensive subscription management system.
