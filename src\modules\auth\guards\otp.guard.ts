import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, Not, Repository } from 'typeorm';
import { OtpVerification } from '../entities/otp-verification.entity';
import {
  OTP_VERIFICATION_REQUIRED,
  OTP_EXPIRED,
  INVALID_OTP,
  REQUIRE_OTP_KEY,
} from '../../../common/constants/otp.constants';

/**
 * Guard to protect routes that require OTP verification
 */
@Injectable()
export class OtpGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    @InjectRepository(OtpVerification)
    private otpVerificationRepository: Repository<OtpVerification>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if the route requires OTP verification
    const requireOtp = this.reflector.getAllAndOverride<boolean>(
      REQUIRE_OTP_KEY,
      [context.getHandler(), context.getClass()],
    );

    // If OTP is not required for this route, allow access
    if (!requireOtp) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new UnauthorizedException(INVALID_OTP);
    }

    // Find the latest verified OTP for this user
    const latestVerifiedOtp = await this.otpVerificationRepository.findOne({
      where: {
        memberId: user.userId,
        verifiedAt: Not(IsNull()),
      },
      order: {
        verifiedAt: 'DESC',
      },
    });

    // Check if OTP has been verified
    if (!latestVerifiedOtp) {
      throw new ForbiddenException(OTP_VERIFICATION_REQUIRED);
    }

    // Optional: Check if the OTP verification is still valid (not expired)
    const now = new Date();
    if (latestVerifiedOtp.expiresAt < now) {
      throw new ForbiddenException(OTP_EXPIRED);
    }

    // User has a valid OTP verification, allow access
    return true;
  }
}
