import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DoubleRatchetService } from './services/double-ratchet.service';
import { GroupEncryptionService } from './services/group-encryption.service';
import { KeyDerivationService } from './services/key-derivation.service';
import { KeyRotationService } from './services/key-rotation.service';
import { PrekeyManagementService } from './services/prekey-management.service';
import { SessionManagementService } from './services/session-management.service';
import { SignalProtocolService } from './services/signal-protocol.service';
import { SignatureService } from './services/signature.service';
import { X3dhService } from './services/x3dh.service';

/**
 * Security Module
 *
 * Handles cryptographic operations, key management,
 * Signal Protocol implementation, and security-related
 * services for end-to-end encryption.
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Add security-related entities here when created
    ]),
  ],
  controllers: [
    // Add security controllers here when created
  ],
  providers: [
    DoubleRatchetService,
    GroupEncryptionService,
    KeyDerivationService,
    KeyRotationService,
    PrekeyManagementService,
    SessionManagementService,
    SignalProtocolService,
    SignatureService,
    X3dhService,
  ],
  exports: [
    DoubleRatchetService,
    GroupEncryptionService,
    KeyDerivationService,
    KeyRotationService,
    PrekeyManagementService,
    SessionManagementService,
    SignalProtocolService,
    SignatureService,
    X3dhService,
  ],
})
export class SecurityModule {}
