import {
  Controller,
  Post,
  Body,
  Param,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { WebrtcService } from '../services/webrtc.service';

/**
 * WebRTC Controller
 *
 * Handles WebRTC-specific HTTP requests including
 * offer/answer exchange, ICE candidates, and connection management.
 */
@ApiTags('webrtc')
@Controller('webrtc')
// @UseGuards(JwtAuthGuard)
export class WebrtcController {
  private readonly logger = new Logger(WebrtcController.name);

  constructor(private readonly webrtcService: WebrtcService) {}

  /**
   * Create WebRTC offer
   */
  @Post('offer')
  @ApiOperation({ summary: 'Create WebRTC offer' })
  @ApiResponse({ status: 201, description: 'Offer created successfully' })
  async createOffer(@Body() offerData: any) {
    const { callId, userId } = offerData;
    return this.webrtcService.createOffer(callId, userId);
  }

  /**
   * Create WebRTC answer
   */
  @Post('answer')
  @ApiOperation({ summary: 'Create WebRTC answer' })
  @ApiResponse({ status: 201, description: 'Answer created successfully' })
  async createAnswer(@Body() answerData: any) {
    const { callId, userId, offer } = answerData;
    return this.webrtcService.createAnswer(callId, userId, offer);
  }

  /**
   * Handle ICE candidate
   */
  @Post('ice-candidate')
  @ApiOperation({ summary: 'Handle ICE candidate' })
  @ApiResponse({
    status: 200,
    description: 'ICE candidate handled successfully',
  })
  async handleIceCandidate(@Body() candidateData: any) {
    const { callId, userId, candidate } = candidateData;
    return this.webrtcService.handleIceCandidate(callId, userId, candidate);
  }

  /**
   * Get ICE servers configuration
   */
  @Post('ice-servers')
  @ApiOperation({ summary: 'Get ICE servers configuration' })
  @ApiResponse({
    status: 200,
    description: 'ICE servers retrieved successfully',
  })
  async getIceServers() {
    return this.webrtcService.getIceServers();
  }
}
