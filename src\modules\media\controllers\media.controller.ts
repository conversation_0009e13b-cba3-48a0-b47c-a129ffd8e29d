import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  Query,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../../../common/guards/jwt-auth.guard';
import { MediaService } from '../services/media.service';
import { MediaUploadService } from '../services/media-upload.service';

/**
 * Media Controller
 *
 * Handles HTTP requests for media file operations including
 * upload, retrieval, metadata extraction, and file management.
 */
@Controller('media')
@UseGuards(JwtAuthGuard)
export class MediaController {
  constructor(
    private readonly mediaService: MediaService,
    private readonly mediaUploadService: MediaUploadService,
  ) {}

  /**
   * Upload media file
   */
  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadMedia(
    @UploadedFile() file: Express.Multer.File,
    @Query('userId', ParseIntPipe) userId: number,
  ) {
    // TODO: Implement media upload endpoint
    return await this.mediaUploadService.uploadFile(file, userId);
  }

  /**
   * Get media file metadata
   */
  @Get(':fileId/metadata')
  async getMediaMetadata(@Param('fileId') fileId: string) {
    // TODO: Implement metadata retrieval endpoint
    return await this.mediaService.getMediaMetadata(fileId);
  }

  /**
   * Get user's media files
   */
  @Get('user/:userId')
  async getUserMediaFiles(
    @Param('userId', ParseIntPipe) userId: number,
    @Query('limit', ParseIntPipe) limit?: number,
    @Query('offset', ParseIntPipe) offset?: number,
  ) {
    // TODO: Implement user media files endpoint
    return await this.mediaService.getUserMediaFiles(userId, limit, offset);
  }

  /**
   * Get media file details
   */
  @Get(':fileId')
  async getMediaFile(@Param('fileId') fileId: string) {
    // TODO: Implement media file details endpoint
    return await this.mediaService.getMediaFileById(fileId);
  }

  /**
   * Delete media file
   */
  @Delete(':fileId')
  async deleteMediaFile(
    @Param('fileId') fileId: string,
    @Query('userId', ParseIntPipe) userId: number,
  ) {
    // TODO: Implement media file deletion endpoint
    await this.mediaService.deleteMediaFile(fileId, userId);
    return { message: 'Media file deleted successfully' };
  }

  /**
   * Process media file (compression, thumbnail generation, etc.)
   */
  @Post(':fileId/process')
  async processMediaFile(
    @Param('fileId') fileId: string,
    @Query('operations') operations: string,
  ) {
    // TODO: Implement media processing endpoint
    return { message: 'Media processing started', fileId, operations };
  }
}
