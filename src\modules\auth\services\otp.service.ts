import {
  Injectable,
  BadRequestException,
  ForbiddenException,
  Logger,
  Inject,
} from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, IsNull, Not, Repository } from 'typeorm';
import { OrgMember } from '../../members/entities/org-member.entity';
import { OtpVerification } from '../entities/otp-verification.entity';
import {
  OTP_VERIFICATION_REQUIRED,
  OTP_EXPIRED,
  INVALID_OTP,
  MAX_OTP_ATTEMPTS,
} from '../../../common/constants/otp.constants';
import { AuthService } from '../services/auth.service';
import { UserType } from '../../../common/types/user-type';
import { VerifyOtpDto } from '../dto/verify-otp.dto';
import { RedisService } from '../../../infrastructure/redis/redis.service';
import { OtpProducer } from '../../../infrastructure/rabbitmq/producers/otp.producer';
import { ConfigService } from '@nestjs/config';
import { MembersService } from '../../members/services/members.service';

@Injectable()
export class OtpService {
  private readonly logger = new Logger(OtpService.name);

  constructor(
    @Inject(MembersService)
    private readonly membersService: MembersService,
    @InjectRepository(OtpVerification)
    private otpVerificationRepository: Repository<OtpVerification>,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly otpProducer: OtpProducer,
    private readonly authService: AuthService,
    private readonly redisService: RedisService,
    private configService: ConfigService,
  ) {}

  /**
   * Generate a random 6-digit OTP
   */
  private generateOtp(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Request OTP for login
   * @param phoneNumber User's phone number
   */
  async requestOtp(phoneNumber: string): Promise<{ message: string }> {
    // Find user
    let member = await this.membersService.findByPhoneNo(phoneNumber);

    // Generate OTP
    const otp = this.generateOtp();

    // Calculate expiration (15 minutes from now)
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 15);

    // Create OTP verification record
    const otpVerification = this.otpVerificationRepository.create({
      memberId: member.id,
      otpCode: otp,
      expiresAt,
      verificationAttempts: 0,
    });

    await this.otpVerificationRepository.save(otpVerification);

    // Send OTP via SMS
    try {
      await this.otpProducer.sendOtpTask({ phoneNumber, otp });
      return { message: 'OTP sent successfully' };
    } catch (error) {
      this.logger.error(`Failed to send OTP SMS: ${error.message}`);
      throw new BadRequestException('Failed to send OTP');
    }
  }

  /**
   * Verify OTP and generate authentication token via AuthService
   * @param phoneNumber User's phone number
   * @param otpCode OTP code entered by user
   */
  async verifyOtp(verifyOtpDto: VerifyOtpDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const member = await queryRunner.manager.findOne(OrgMember, {
        where: { phoneNo: verifyOtpDto.phoneNumber },
        relations: ['organization'],
      });

      if (!member) {
        throw new BadRequestException('User not found');
      }

      const otpVerification = await queryRunner.manager.findOne(
        OtpVerification,
        {
          where: { memberId: member.id },
          order: { id: 'DESC' },
        },
      );

      if (!otpVerification) {
        throw new ForbiddenException(OTP_VERIFICATION_REQUIRED);
      }

      const now = new Date();
      if (otpVerification.expiresAt < now) {
        throw new ForbiddenException(OTP_EXPIRED);
      }

      if (otpVerification.verificationAttempts >= MAX_OTP_ATTEMPTS) {
        throw new ForbiddenException('Maximum verification attempts reached');
      }

      otpVerification.verificationAttempts += 1;

      if (otpVerification.otpCode !== verifyOtpDto.otpCode) {
        await queryRunner.manager.save(otpVerification);
        throw new BadRequestException(INVALID_OTP);
      }

      otpVerification.verifiedAt = new Date();
      await queryRunner.manager.save(otpVerification);

      member.isVerified = true;
      member.lastLoginAt = new Date();
      await queryRunner.manager.save(member);

      const payload = {
        sub: member.id,
        email: member.email,
        type: 'org_member' as UserType,
        orgId: member.orgId,
      };

      const tokens = await this.authService.generateTokens(payload);
      await this.redisService.storeFcmToken(
        member.id,
        verifyOtpDto.fcmToken,
        verifyOtpDto.deviceId,
      );

      // Calculate refresh token expiration in seconds
      const refreshTokenExpiresIn =
        parseInt(
          this.configService
            .get<string>('jwt.refreshExpiresIn', '7d')
            .replace('d', '') || '7',
        ) *
        24 *
        60 *
        60;

      await this.redisService.storeRefreshToken(
        'org_member',
        member.id,
        tokens.tokenId,
        tokens.refreshToken,
        refreshTokenExpiresIn,
      );

      await this.redisService.setMemberOnline(member.id, verifyOtpDto.deviceId);

      await this.membersService.upsertFcmToken(
        queryRunner,
        member.id,
        verifyOtpDto,
      );

      await queryRunner.commitTransaction();
      return tokens;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`OTP verification failed: ${error.message}`);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Check if user has a valid OTP verification
   * Used by OtpGuard
   */
  async hasValidOtpVerification(memberId: number): Promise<boolean> {
    try {
      const latestVerifiedOtp = await this.otpVerificationRepository.findOne({
        where: {
          memberId,
          verifiedAt: Not(IsNull()),
        },
        order: {
          verifiedAt: 'DESC',
        },
      });

      if (!latestVerifiedOtp) {
        return false;
      }

      // Check if OTP has expired
      const now = new Date();
      return latestVerifiedOtp.expiresAt > now;
    } catch (error) {
      this.logger.error(`OTP verification check failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Resend OTP verification code
   * @param phoneNumber User's phone number
   */
  async resendOtp(phoneNumber: string): Promise<{ message: string }> {
    // Find user
    const member = await this.membersService.findByPhoneNo(phoneNumber);
    // Generate new OTP
    return this.requestOtp(phoneNumber);
  }

  async logout(memberId: number, deviceId: string) {
    await this.redisService.setMemberOffline(memberId, deviceId);
    await this.redisService.removeFcmToken(memberId, deviceId);

    // Optionally, mark device session as inactive in DB
    await this.membersService.invalidToken(memberId, deviceId);
  }
}
