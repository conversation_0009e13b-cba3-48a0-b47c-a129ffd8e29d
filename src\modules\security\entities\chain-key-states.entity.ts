import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { OrgMember } from '../../members/entities/org-member.entity';

@Entity('chain_key_states')
@Index(['groupId', 'memberId', 'isActive'])
@Index(['pairId', 'memberId', 'isActive'])
export class ChainKeyState {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  groupId: number;

  @Column({ nullable: true })
  pairId: number;

  @Column()
  memberId: number;

  @Column({ type: 'text' })
  chainKey: string;

  @Column({ default: 0 })
  messageNumber: number;

  @Column()
  keyVersion: number;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => OrgMember)
  @JoinColumn({ name: 'memberId' })
  member: OrgMember;
}
