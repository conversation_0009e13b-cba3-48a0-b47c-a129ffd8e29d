import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';

@Injectable()
export class RabbitMQMonitorService implements OnModuleInit {
  private readonly logger = new Logger(RabbitMQMonitorService.name);

  constructor(private readonly amqpConnection: AmqpConnection) {}

  onModuleInit() {
    const connection = this.amqpConnection.managedConnection;

    connection.on('connect', () => {
      this.logger.log('✅ RabbitMQ Connected');
    });

    connection.on('disconnect', () => {
      this.logger.warn('⚠️ RabbitMQ Disconnected - Auto-reconnecting...');
    });

    connection.on('connectFailed', (err) => {
      this.logger.error('❌ RabbitMQ Connection Failed:', err);
    });
  }
}
