import { MigrationInterface, QueryRunner } from "typeorm";

export class InitMigrations1751601606891 implements MigrationInterface {
    name = 'InitMigrations1751601606891'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "roles" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "description" character varying, CONSTRAINT "UQ_648e3f5447f725579d7d4ffdfb7" UNIQUE ("name"), CONSTRAINT "PK_c1433d71a4838793a49dcad46ab" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."member_fcm_tokens_device_type_enum" AS ENUM('android', 'ios')`);
        await queryRunner.query(`CREATE TABLE "member_fcm_tokens" ("id" SERIAL NOT NULL, "member_id" integer NOT NULL, "fcm_token" character varying(255) NOT NULL, "device_id" character varying, "device_type" "public"."member_fcm_tokens_device_type_enum", "is_active" boolean NOT NULL DEFAULT true, "last_used_at" TIMESTAMP, "deleted_at" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_5c24dec108128242dfc478c50b1" UNIQUE ("member_id", "device_id"), CONSTRAINT "PK_2fbeb9c84c1f58e127ee22ee601" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_9c590c37351aae3272ddb4e3f1" ON "member_fcm_tokens" ("member_id") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_32999054fcef8ce59ad964068e" ON "member_fcm_tokens" ("fcm_token") `);
        await queryRunner.query(`CREATE TABLE "group_encryption_keys" ("id" SERIAL NOT NULL, "group_id" integer NOT NULL, "member_id" integer NOT NULL, "encrypted_group_key" text NOT NULL, "key_version" integer NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_25c9efb4a8674ea272e06e60ec4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_7078f7f199770c354643d4da4c" ON "group_encryption_keys" ("group_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_9d7ecf0b23c1538b67bf835f6c" ON "group_encryption_keys" ("member_id") `);
        await queryRunner.query(`CREATE TABLE "org_members" ("id" SERIAL NOT NULL, "org_id" integer NOT NULL, "name" character varying NOT NULL, "phone_no" character varying NOT NULL, "email" character varying NOT NULL, "image_url" character varying, "is_verified" boolean NOT NULL DEFAULT false, "last_login_at" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "publicKey" text, "encryptedPrivateKey" text, "keyCreatedAt" TIMESTAMP, CONSTRAINT "UQ_9c5e820b3c831be541e0960b2ec" UNIQUE ("phone_no"), CONSTRAINT "UQ_6221142498ac4910f1236711c8b" UNIQUE ("email"), CONSTRAINT "PK_8391a72b91725161ab2cab00be9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "groups" ("id" SERIAL NOT NULL, "org_id" integer NOT NULL, "name" character varying NOT NULL, "description" character varying, "is_active" boolean NOT NULL DEFAULT true, "image_url" character varying, "created_by" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deletedAt" TIMESTAMP, "deletedBy" integer, "current_key_version" integer NOT NULL DEFAULT '1', "deleted_by" integer, CONSTRAINT "PK_659d1483316afb28afd3a90646e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."organization_status_enum" AS ENUM('active', 'inactive')`);
        await queryRunner.query(`CREATE TABLE "organization" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "location" character varying NOT NULL, "phone_no" character varying NOT NULL, "image_url" character varying, "status" "public"."organization_status_enum" NOT NULL DEFAULT 'active', CONSTRAINT "UQ_c21e615583a3ebbb0977452afb0" UNIQUE ("name"), CONSTRAINT "PK_472c1f99a32def1b0abb219cd67" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "users" ("id" SERIAL NOT NULL, "username" character varying NOT NULL, "password" character varying NOT NULL, "email" character varying NOT NULL, "image_url" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "role_id" integer NOT NULL, "org_id" integer, "encryptedAdminSecretKey" character varying, "publicKey" character varying, "adminSecretKeyNonce" character varying, "adminSecretKeySalt" character varying, CONSTRAINT "UQ_fe0bb3f6520ee0469504521e710" UNIQUE ("username"), CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "files" ("id" SERIAL NOT NULL, "filename" character varying(500) NOT NULL, "file_url" text NOT NULL, "file_type" character varying(100) NOT NULL, "file_size" integer NOT NULL, "meta_data" jsonb, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_6c16b9093a142e0e7613b04a3d9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "messages" ("id" SERIAL NOT NULL, "group_id" integer NOT NULL, "sender_id" integer NOT NULL, "file_id" integer, "sent_at" TIMESTAMP NOT NULL, "encrypted_content" character varying, "encrypted_meta_data" jsonb, "nonce" character varying, "group_key_version" integer NOT NULL, "is_deleted" boolean NOT NULL DEFAULT false, "deletedAt" TIMESTAMP, "deletedBy" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "reply_to_message_id" integer, CONSTRAINT "PK_18325f38ae6de43878487eff986" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "message_visibility" ("id" SERIAL NOT NULL, "message_id" integer NOT NULL, "member_id" integer NOT NULL, "is_visible" boolean NOT NULL DEFAULT true, "deleted_at" TIMESTAMP, CONSTRAINT "PK_578343330e3a4c7d4b23086859d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "group_message_deliveries" ("id" SERIAL NOT NULL, "message_id" integer NOT NULL, "delivered_to" integer NOT NULL, "delivered_at" TIMESTAMP NOT NULL, CONSTRAINT "PK_39c403c608b6ae4b0560a700fa1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "group_message_reads" ("id" SERIAL NOT NULL, "message_id" integer NOT NULL, "reader_id" integer NOT NULL, "read_at" TIMESTAMP NOT NULL, CONSTRAINT "PK_3dc8256e5a5f0c08c07a5688f42" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "group_members" ("id" SERIAL NOT NULL, "group_id" integer NOT NULL, "member_id" integer NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "joined_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "left_at" TIMESTAMP, "is_mute" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_86446139b2c96bfd0f3b8638852" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "otp_verification" ("id" SERIAL NOT NULL, "member_id" integer NOT NULL, "otp_code" character varying NOT NULL, "expires_at" TIMESTAMP NOT NULL, "verified_at" TIMESTAMP, "verification_attempts" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_090ea63a8ef4f33b1a5f29924f8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "member_fcm_tokens" ADD CONSTRAINT "FK_9c590c37351aae3272ddb4e3f1f" FOREIGN KEY ("member_id") REFERENCES "org_members"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_encryption_keys" ADD CONSTRAINT "FK_7078f7f199770c354643d4da4cc" FOREIGN KEY ("group_id") REFERENCES "groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_encryption_keys" ADD CONSTRAINT "FK_9d7ecf0b23c1538b67bf835f6cc" FOREIGN KEY ("member_id") REFERENCES "org_members"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "org_members" ADD CONSTRAINT "FK_a35e7519ef33c0dd4d24bb15056" FOREIGN KEY ("org_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "groups" ADD CONSTRAINT "FK_b7379dfe5898f32ecdae845b49c" FOREIGN KEY ("org_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "groups" ADD CONSTRAINT "FK_a2fa29bfd5351b5b7ccacbc9f7c" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "groups" ADD CONSTRAINT "FK_ed1365ad8a7c673ccb918ee12a6" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "users" ADD CONSTRAINT "FK_a2cecd1a3531c0b041e29ba46e1" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "users" ADD CONSTRAINT "FK_0a13270cd3101fd16b8000e00d4" FOREIGN KEY ("org_id") REFERENCES "organization"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "messages" ADD CONSTRAINT "FK_7f87cbb925b1267778a7f4c5d67" FOREIGN KEY ("reply_to_message_id") REFERENCES "messages"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "messages" ADD CONSTRAINT "FK_d3607f0e3fc4217505168fc3932" FOREIGN KEY ("group_id") REFERENCES "groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "messages" ADD CONSTRAINT "FK_22133395bd13b970ccd0c34ab22" FOREIGN KEY ("sender_id") REFERENCES "org_members"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "messages" ADD CONSTRAINT "FK_fcce42c9a20b120df9a5b672569" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message_visibility" ADD CONSTRAINT "FK_ef165b20d1a539b22edcd0730a9" FOREIGN KEY ("message_id") REFERENCES "messages"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message_visibility" ADD CONSTRAINT "FK_4e001ad733be56d4f206b2e657b" FOREIGN KEY ("member_id") REFERENCES "org_members"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_message_deliveries" ADD CONSTRAINT "FK_b7dde77fef9b3cc85d434782177" FOREIGN KEY ("message_id") REFERENCES "messages"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_message_deliveries" ADD CONSTRAINT "FK_a2b433d397dc1934c2d08db9168" FOREIGN KEY ("delivered_to") REFERENCES "org_members"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_message_reads" ADD CONSTRAINT "FK_d5287aaa92221be2f084d13712a" FOREIGN KEY ("message_id") REFERENCES "messages"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_message_reads" ADD CONSTRAINT "FK_267a6900cbc782e1fc9d1e556ad" FOREIGN KEY ("reader_id") REFERENCES "org_members"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_members" ADD CONSTRAINT "FK_2c840df5db52dc6b4a1b0b69c6e" FOREIGN KEY ("group_id") REFERENCES "groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "group_members" ADD CONSTRAINT "FK_dd36c2f163638fffa2edd4e44f2" FOREIGN KEY ("member_id") REFERENCES "org_members"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "otp_verification" ADD CONSTRAINT "FK_5fd7d4427ccf844a9f70cb7fb2b" FOREIGN KEY ("member_id") REFERENCES "org_members"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "otp_verification" DROP CONSTRAINT "FK_5fd7d4427ccf844a9f70cb7fb2b"`);
        await queryRunner.query(`ALTER TABLE "group_members" DROP CONSTRAINT "FK_dd36c2f163638fffa2edd4e44f2"`);
        await queryRunner.query(`ALTER TABLE "group_members" DROP CONSTRAINT "FK_2c840df5db52dc6b4a1b0b69c6e"`);
        await queryRunner.query(`ALTER TABLE "group_message_reads" DROP CONSTRAINT "FK_267a6900cbc782e1fc9d1e556ad"`);
        await queryRunner.query(`ALTER TABLE "group_message_reads" DROP CONSTRAINT "FK_d5287aaa92221be2f084d13712a"`);
        await queryRunner.query(`ALTER TABLE "group_message_deliveries" DROP CONSTRAINT "FK_a2b433d397dc1934c2d08db9168"`);
        await queryRunner.query(`ALTER TABLE "group_message_deliveries" DROP CONSTRAINT "FK_b7dde77fef9b3cc85d434782177"`);
        await queryRunner.query(`ALTER TABLE "message_visibility" DROP CONSTRAINT "FK_4e001ad733be56d4f206b2e657b"`);
        await queryRunner.query(`ALTER TABLE "message_visibility" DROP CONSTRAINT "FK_ef165b20d1a539b22edcd0730a9"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP CONSTRAINT "FK_fcce42c9a20b120df9a5b672569"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP CONSTRAINT "FK_22133395bd13b970ccd0c34ab22"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP CONSTRAINT "FK_d3607f0e3fc4217505168fc3932"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP CONSTRAINT "FK_7f87cbb925b1267778a7f4c5d67"`);
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_0a13270cd3101fd16b8000e00d4"`);
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_a2cecd1a3531c0b041e29ba46e1"`);
        await queryRunner.query(`ALTER TABLE "groups" DROP CONSTRAINT "FK_ed1365ad8a7c673ccb918ee12a6"`);
        await queryRunner.query(`ALTER TABLE "groups" DROP CONSTRAINT "FK_a2fa29bfd5351b5b7ccacbc9f7c"`);
        await queryRunner.query(`ALTER TABLE "groups" DROP CONSTRAINT "FK_b7379dfe5898f32ecdae845b49c"`);
        await queryRunner.query(`ALTER TABLE "org_members" DROP CONSTRAINT "FK_a35e7519ef33c0dd4d24bb15056"`);
        await queryRunner.query(`ALTER TABLE "group_encryption_keys" DROP CONSTRAINT "FK_9d7ecf0b23c1538b67bf835f6cc"`);
        await queryRunner.query(`ALTER TABLE "group_encryption_keys" DROP CONSTRAINT "FK_7078f7f199770c354643d4da4cc"`);
        await queryRunner.query(`ALTER TABLE "member_fcm_tokens" DROP CONSTRAINT "FK_9c590c37351aae3272ddb4e3f1f"`);
        await queryRunner.query(`DROP TABLE "otp_verification"`);
        await queryRunner.query(`DROP TABLE "group_members"`);
        await queryRunner.query(`DROP TABLE "group_message_reads"`);
        await queryRunner.query(`DROP TABLE "group_message_deliveries"`);
        await queryRunner.query(`DROP TABLE "message_visibility"`);
        await queryRunner.query(`DROP TABLE "messages"`);
        await queryRunner.query(`DROP TABLE "files"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP TABLE "organization"`);
        await queryRunner.query(`DROP TYPE "public"."organization_status_enum"`);
        await queryRunner.query(`DROP TABLE "groups"`);
        await queryRunner.query(`DROP TABLE "org_members"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9d7ecf0b23c1538b67bf835f6c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7078f7f199770c354643d4da4c"`);
        await queryRunner.query(`DROP TABLE "group_encryption_keys"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_32999054fcef8ce59ad964068e"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9c590c37351aae3272ddb4e3f1"`);
        await queryRunner.query(`DROP TABLE "member_fcm_tokens"`);
        await queryRunner.query(`DROP TYPE "public"."member_fcm_tokens_device_type_enum"`);
        await queryRunner.query(`DROP TABLE "roles"`);
    }

}
