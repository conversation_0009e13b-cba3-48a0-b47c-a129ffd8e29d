import { Transform } from 'class-transformer';
import {
  IsEmail,
  IsOptional,
  IsString,
  IsNumber,
  IsBoolean,
  isNotEmpty,
  ArrayNotEmpty,
  IsArray,
  IsNotEmpty,
  ArrayUnique,
} from 'class-validator';

export class UpdateGroupMemberDto {
  @IsNotEmpty()
  @IsNumber()
  groupId: number;

  @IsNotEmpty()
  @IsNumber({}, { each: true })
  updatedMemberIds: number[];

  @IsNotEmpty()
  @IsString()
  adminSecretKey: string;
}
